# CD for functions
name: functions-cd

# Controls when the workflow will run
on:
  push:
    branches: [qa, dev, main, dev2]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  install:
  
    environment: ${{ github.ref_name }}
    name: Install
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install Yarn
        run: npm install -g yarn
      
      - name: Cache node modules
        uses: actions/cache@v4
        id: cache-node-modules
        with:
          path: ./node_modules
          enableCrossOsArchive: true
          key: node_modules-${{ hashFiles('yarn.lock') }}
          restore-keys: |
            node_modules-

      - name: Install dependencies
        if: steps.cache-node-modules.outputs.cache-hit != 'true'
        run: yarn install --frozen-lockfile

  audit:
    environment: ${{ github.ref_name }}
    name: Audit
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install Yarn
        run: npm install -g yarn

      - name: Audit
        run: yarn run audit

  linter:
    environment: ${{ github.ref_name }}
    name: <PERSON><PERSON>
    needs: [install, audit]
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install Yarn
        run: npm install -g yarn

      - name: Download node_modules
        uses: actions/cache@v4
        id: cache_node_modules
        with:
          path: ./node_modules
          key: node_modules-${{ hashFiles('yarn.lock') }}
          enableCrossOsArchive: true
          restore-keys: |
            node_modules-

      - name: Build
        run: yarn build

      - name: Linter
        run: yarn lint

  tests:
    environment: ${{ github.ref_name }}
    name: Tests
    needs: [install, audit]
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install Yarn
        run: npm install -g yarn

      - name: Download node_modules
        uses: actions/cache@v4
        id: cache_node_modules
        with:
          path: ./node_modules
          key: node_modules-${{ hashFiles('yarn.lock') }}
          enableCrossOsArchive: true
          restore-keys: |
            node_modules-

      - name: Build
        run: yarn build

      - name: Test
        run: yarn test

  build:
    environment: ${{ github.ref_name }}
    name: Build
    needs: [install, audit]
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install Yarn
        run: npm install -g yarn

      - name: Download node_modules
        uses: actions/cache@v4
        id: cache_node_modules
        with:
          path: ./node_modules
          key: node_modules-${{ hashFiles('yarn.lock') }}
          enableCrossOsArchive: true
          restore-keys: |
            node_modules-

      - name: Build
        run: yarn build

      - name: Cache lib
        uses: actions/cache@v4
        id: cache_lib
        with:
          path: ./lib
          key: lib_${{ github.sha }}

      - name: Archive build
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: ./lib

  deploy:
    environment: ${{ github.ref_name }}
    name: Deploy
    needs: [linter, tests, build]
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install Yarn
        run: npm install -g yarn

      - name: Download node_modules
        uses: actions/cache@v4
        id: cache_node_modules
        with:
          path: ./node_modules
          key: node_modules-${{ hashFiles('yarn.lock') }}
          enableCrossOsArchive: true
          restore-keys: |
            node_modules-

      - name: Download lib
        uses: actions/cache@v4
        id: cache_lib
        with:
          path: ./lib
          key: lib_${{ github.sha }}

      - name: Deploy to GCP
        uses: w9jds/firebase-action@v13.6.0
        with:
          args: deploy --only functions --force
        env:
          PROJECT_ID: ${{ vars.PROJECT_ID }}
          GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}

  update-rules:
    environment: ${{ github.ref_name }}
    name: Update Rules and Indexes
    needs: [linter, tests, build]
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install Yarn
        run: npm install -g yarn

      - name: Download node_modules
        uses: actions/cache@v4
        id: cache_node_modules
        with:
          path: ./node_modules
          key: node_modules-${{ hashFiles('yarn.lock') }}
          enableCrossOsArchive: true
          restore-keys: |
            node_modules-

      - name: Download lib
        uses: actions/cache@v4
        id: cache_lib
        with:
          path: ./lib
          key: lib_${{ github.sha }}

      - name: Update Firestore Rules and Indexes
        uses: w9jds/firebase-action@v13.6.0
        with:
          args: deploy --only firestore --force
        env:
          PROJECT_ID: ${{ vars.PROJECT_ID }}
          GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}

  sentry:
    environment: ${{ github.ref_name }}
    name: Sentry Setup
    needs: [linter, tests, build]
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install Yarn
        run: npm install -g yarn

      - name: Download node_modules
        uses: actions/cache@v4
        id: cache_node_modules
        with:
          path: ./node_modules
          key: node_modules-${{ hashFiles('yarn.lock') }}
          enableCrossOsArchive: true
          restore-keys: |
            node_modules-

      - name: Download lib
        uses: actions/cache@v4
        id: cache_lib
        with:
          path: ./lib
          key: lib_${{ github.sha }}

      - name: Sourcemaps
        run: yarn sentry:sourcemaps
