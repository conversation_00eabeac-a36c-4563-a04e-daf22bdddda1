import { randomUUID } from "crypto";

import { PAYMENT_MERCHANT_INFORMATION_MERCHANT_DESCRIPTOR_AUTH } from "@nest/modules/payment/modules/paymentInstrument/modules/globalPayment/dto/globalPayment.dto";

import { TxAppsNames } from "../../../../../nestJs/modules/apps/dto/Apps.dto";
import PaymentInstrument from "../../../../../nestJs/modules/database/entities/paymentInstrument.entity";
import User from "../../../../../nestJs/modules/database/entities/user.entity";
import { CreatePaymentInstrumentBody } from "../../../../../nestJs/modules/me/modules/mePaymentInstrument/mePaymentInstrument.dto";
import { PaymentGatewayTypes } from "../../../../../nestJs/modules/payment/dto/paymentGatewayTypes.dto";
import {
  PaymentInstrumentState,
  PaymentInstrumentType,
} from "../../../../../nestJs/modules/payment/modules/paymentInstrument/dto/paymentInstrument.dto";
import { PaymentInstrumentService } from "../../../../../nestJs/modules/payment/modules/paymentInstrument/paymentInstrument.service";
import { errorBuilder } from "../../../../../nestJs/modules/utils/utils/error.utils";
import {
  InstrumentIdentifierApiMock,
  PayerAuthenticationApiMock,
  PaymentsApiMock,
  bindingMock,
  createPaymentMock,
  promisifyMock,
} from "../../../../utils/cybersource-rest-client/cybersourceRestClientMocks.specs.utils";
import expects from "../../../../utils/expects.specs.utils";
import {
  mockAppByNameOrCreate,
  mockCount,
  mockDelete,
  mockFindOne,
  mockGetOne,
  mockSave,
} from "../../../../utils/services/FakeRepository.specs.utils";
import { newTestPaymentInstrumentService } from "../../../../utils/services/TestServices.specs.utils";

import "../../../../initTests";

// const defaultConfig = {
//   authenticationType: "http_signature",
//   keyAlias: "GLOBAL_PAYMENT_KEY_FILENAME",
//   keyFileName: "GLOBAL_PAYMENT_KEY_FILENAME",
//   keyPass: "GLOBAL_PAYMENT_KEY_FILENAME",
//   keysDirectory: "Resource",
//   logConfiguration: {
//     enableLog: true,
//     enableMasking: true,
//     logDirectory: "log",
//     logFileMaxSize: "5242880",
//     logFileName: "cybs",
//     loggingLevel: "debug",
//   },
//   merchantID: "GLOBAL_PAYMENT_MERCHANT_ID",
//   merchantKeyId: "GLOBAL_PAYMENT_MERCHANT_KEY_ID",
//   merchantsecretKey: "GLOBAL_PAYMENT_MERCHANT_KEY_SECRET",
//   pemFileDirectory: "Resource/NetworkTokenCert.pem",
//   portfolioID: "",
//   runEnvironment: "GLOBAL_PAYMENT_ENV.cybersource.com",
//   useMetaKey: false,
// };

describe("paymentInstrument.service", () => {
  let service: PaymentInstrumentService;
  const userId = "IkCO7YHOJ3c073P8RihpRZxX3yC2";
  const paymentInstrumentRequestBody: CreatePaymentInstrumentBody = {
    cardNumber: "****************",
    expirationYear: "2031",
    expirationMonth: "12",
    cardHolderName: "John Doe",
    securityCode: "777",
    cardType: PaymentInstrumentType.VISA,
  };
  const jsonPaymentInstrument: Partial<PaymentInstrument> = {
    id: randomUUID(),
    token: "Token",
    instrumentIdentifier: "1234",
    cardHolderName: "John Doe",
    verifiedAt: undefined,
    expirationDate: expect.any(Date),
    expirationYear: "2031",
    expirationMonth: "12",
    state: PaymentInstrumentState.ACTIVE,
    cardPrefix: "412345",
    cardSuffix: "3456",
    cardType: PaymentInstrumentType.VISA,
    isPayerAuthEnroled: true,
    isPreferred: false,
    updateExpirationDate: expect.any(Function),
    paymentGateway: PaymentGatewayTypes.GLOBAL_PAYMENTS,
    user: {
      id: userId,
    } as User,
  };
  const defaultPaymentInstrument = {
    ...jsonPaymentInstrument,
    id: expect.stringMatching(expects.uuid),
    token: expect.any(String),
    toJson: expect.any(Function),
  };

  beforeEach(async () => {
    service = newTestPaymentInstrumentService();

    PayerAuthenticationApiMock.mockImplementation(() => {
      return {
        payerAuthSetup: bindingMock(promisifyMock),
        validateAuthenticationResults: bindingMock(promisifyMock),
        checkPayerAuthEnrollment: bindingMock(promisifyMock),
      };
    });

    mockAppByNameOrCreate.mockResolvedValue({ name: TxAppsNames.VISMO, id: "1234" });

    mockGetOne.mockResolvedValue({ id: "1234", metadata: {} });
  });

  describe("createPaymentInstrument", () => {
    const postInstrumentIdentifierMock = jest.fn();

    beforeEach(() => {
      postInstrumentIdentifierMock.mockImplementation((...args: any[]) =>
        /// get last arg
        args[args.length - 1](null, {
          id: "1234",
          state: "ACTIVE",
        }),
      );
      mockFindOne.mockResolvedValueOnce({
        id: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        email: "<EMAIL>",
      });

      mockFindOne.mockResolvedValueOnce(null);

      mockSave.mockResolvedValue(Promise.resolve());

      InstrumentIdentifierApiMock.mockImplementation(() => {
        return {
          postInstrumentIdentifier: bindingMock(postInstrumentIdentifierMock),
        };
      });
    });

    afterEach(() => {
      mockSave.mockReset();
    });

    it("should throw an error when user not found", async () => {
      mockFindOne.mockReset();
      mockFindOne.mockResolvedValueOnce(null);

      mockFindOne.mockResolvedValueOnce(defaultPaymentInstrument);

      await expect(
        service.createPaymentInstrument(paymentInstrumentRequestBody, userId, PaymentGatewayTypes.GLOBAL_PAYMENTS),
      ).rejects.toThrow(errorBuilder.user.notFoundInSql(userId));
    });

    it("should throw an error when sql fails", async () => {
      const error = new Error("error");
      mockSave.mockRejectedValue(error);

      await expect(
        service.createPaymentInstrument(paymentInstrumentRequestBody, userId, PaymentGatewayTypes.GLOBAL_PAYMENTS),
      ).rejects.toThrow();

      expect(mockDelete).not.toHaveBeenCalled();
    });
  });

  describe("initiate3DSecure", () => {
    const payerAuthSetupMock = jest.fn();

    beforeEach(() => {
      payerAuthSetupMock.mockImplementation((...args: any[]) =>
        /// get last arg
        args[args.length - 1](null, {
          consumerAuthenticationInformation: {
            accessToken: "accessToken",
            referenceId: "referenceId",
            deviceDataCollectionUrl: "deviceDataCollectionUrl",
          },
          status: "COMPLETED",
        }),
      );
      mockFindOne.mockResolvedValueOnce({
        id: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        email: "<EMAIL>",
      });

      mockFindOne.mockResolvedValueOnce(PaymentInstrument.fromJson(defaultPaymentInstrument));

      PayerAuthenticationApiMock.mockImplementation(() => {
        return {
          payerAuthSetup: bindingMock(payerAuthSetupMock),
        };
      });

      mockAppByNameOrCreate.mockResolvedValue({ name: TxAppsNames.VISMO, id: "1234" });
    });

    it("should throw an error when the user is not found", async () => {
      mockFindOne.mockReset();
      mockFindOne.mockResolvedValueOnce(null);

      mockFindOne.mockResolvedValueOnce(defaultPaymentInstrument);

      await expect(service.initiate3DSecure({ paymentInstrumentId: "1234" }, userId)).rejects.toThrow(
        errorBuilder.user.notFoundInSql(userId),
      );
    });

    it("should throw an error when the payment instrument is not found", async () => {
      mockFindOne.mockReset();
      mockFindOne.mockResolvedValueOnce({
        id: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        email: "",
      });

      mockFindOne.mockResolvedValueOnce(null);

      await expect(service.initiate3DSecure({ paymentInstrumentId: "1234" }, userId)).rejects.toThrow(
        errorBuilder.payment.instrument.notFound("1234"),
      );
    });
  });

  describe("getPaymentInstrument", () => {
    beforeEach(() => {
      mockFindOne.mockResolvedValueOnce({
        id: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        email: "",
      });

      mockFindOne.mockResolvedValueOnce({ publicData: defaultPaymentInstrument });
    });

    it("should return the payment instrument", async () => {
      const result = await service.getPaymentInstrument({ paymentInstrumentId: "1234" }, userId);

      expect(result).toEqual(defaultPaymentInstrument);
    });

    it("should throw an error when the user is not found", async () => {
      mockFindOne.mockReset();
      mockFindOne.mockResolvedValueOnce(null);

      mockFindOne.mockResolvedValueOnce(defaultPaymentInstrument);

      await expect(service.getPaymentInstrument({ paymentInstrumentId: "1234" }, userId)).rejects.toThrow(
        errorBuilder.user.notFoundInSql(userId),
      );
    });

    it("should throw an error when the payment instrument is not found", async () => {
      mockFindOne.mockReset();
      mockFindOne.mockResolvedValueOnce({
        id: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        email: "",
      });

      mockFindOne.mockResolvedValueOnce(null);

      await expect(service.getPaymentInstrument({ paymentInstrumentId: "1234" }, userId)).rejects.toThrow(
        errorBuilder.payment.instrument.notFound("1234"),
      );
    });
  });

  describe("createPayment", () => {
    const createPaymentMock = jest.fn();

    beforeEach(() => {
      createPaymentMock.mockImplementation((...args: any[]) =>
        /// get last arg
        args[args.length - 1](null, {
          status: "PENDING_AUTHENTICATION",
          consumerAuthenticationInformation: {
            accessToken: "accessToken",
            stepUpUrl: "stepUpUrl",
          },
        }),
      );
      mockFindOne.mockResolvedValueOnce({
        id: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        email: "<EMAIL>",
      });

      mockFindOne.mockResolvedValueOnce(PaymentInstrument.fromJson({ ...defaultPaymentInstrument, id: "1234" }));

      PaymentsApiMock.mockImplementation(() => {
        return {
          createPayment: bindingMock(createPaymentMock),
        };
      });
    });

    // it("should process 3D Secure", async () => {
    //   const result = await service.createPayment(
    //     {
    //       paymentInstrumentId: "1234",
    //       sessionId: "3456",
    //     },
    //     {
    //       httpBrowserScreenHeight: "850",
    //       httpBrowserScreenWidth: "500",
    //       ipAddress: "12.345.678.90",
    //     },
    //     userId,
    //   );

    //   expect(result).toEqual({
    //     accessToken: "accessToken",
    //     stepUpUrl: "stepUpUrl",
    //     status: "VERIFICATION_PENDING",
    //     id: undefined,
    //     raw: {
    //       consumerAuthenticationInformation: {
    //         accessToken: "accessToken",
    //         stepUpUrl: "stepUpUrl",
    //       },
    //       status: "PENDING_AUTHENTICATION",
    //     },
    //   });

    //   expect(PaymentsApiMock).toHaveBeenCalledWith(defaultConfig, {});
    //   expect(createPaymentMock).toHaveBeenCalledWith(
    //     {
    //       clientReferenceInformation: {
    //         code: "GLOBAL_PAYMENT_CLIENT_REFERENCE_CODE",
    //       },
    //       consumerAuthenticationInformation: {
    //         referenceId: "3456",
    //         returnUrl: "GLOBAL_PAYMENT_BACKEND_URL/global-payments/3d-secure/notification",
    //         messageCategory: "02",
    //       },
    //       deviceInformation: {
    //         httpBrowserScreenHeight: "850",
    //         httpBrowserScreenWidth: "500",
    //         ipAddress: "12.345.678.90",
    //       },
    //       orderInformation: {
    //         amountDetails: {
    //           currency: "HKD",
    //           totalAmount: service.AUTH_AMOUNT_WHEN_ADD_CARD.toString(),
    //         },
    //       },
    //       paymentInformation: {
    //         paymentInstrument: {
    //           id: expect.any(String),
    //         },
    //       },
    //       processingInformation: {
    //         actionList: ["CONSUMER_AUTHENTICATION"],
    //         capture: false,
    //       },
    //       merchantInformation: {
    //         merchantDescriptor: { name: PAYMENT_MERCHANT_INFORMATION_MERCHANT_DESCRIPTOR_AUTH },
    //       },
    //     },
    //     expect.any(Function),
    //   );
    // });

    it("should throw an error when the user is not found", async () => {
      mockFindOne.mockReset();
      mockFindOne.mockResolvedValueOnce(null);

      mockFindOne.mockResolvedValueOnce(defaultPaymentInstrument);

      await expect(
        service.createPayment(
          {
            paymentInstrumentId: "1234",
            sessionId: "3456",
          },
          {
            httpBrowserScreenHeight: "850",
            httpBrowserScreenWidth: "500",
            ipAddress: "12.345.678.90",
          },
          userId,
        ),
      ).rejects.toThrow(errorBuilder.user.notFoundInSql(userId));
    });

    it("should throw an error when the payment instrument is not found", async () => {
      mockFindOne.mockReset();
      mockFindOne.mockResolvedValueOnce({
        id: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        email: "",
      });

      mockFindOne.mockResolvedValueOnce(null);

      await expect(
        service.createPayment(
          {
            paymentInstrumentId: "1234",
            sessionId: "3456",
          },
          {
            httpBrowserScreenHeight: "850",
            httpBrowserScreenWidth: "500",
            ipAddress: "12.345.678.90",
          },
          userId,
        ),
      ).rejects.toThrow(errorBuilder.payment.instrument.notFound("1234"));
    });

    it("should throw an error when the status is not VERIFICATION_PENDING", async () => {
      createPaymentMock.mockImplementation((...args: any[]) =>
        /// get last arg
        args[args.length - 1](null, {
          status: "COMPLETED",
        }),
      );

      await expect(
        service.createPayment(
          {
            paymentInstrumentId: "1234",
            sessionId: "3456",
          },
          {
            httpBrowserScreenHeight: "850",
            httpBrowserScreenWidth: "500",
            ipAddress: "12.345.678.90",
          },
          userId,
        ),
      ).rejects.toThrow(errorBuilder.payment.instrument.threeDSecure.notCompatible("1234", "COMPLETED"));
    });
  });

  describe("receiveTransaction", () => {
    beforeEach(() => {
      mockFindOne.mockResolvedValueOnce({
        id: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        email: "<EMAIL>",
      });

      mockFindOne.mockResolvedValueOnce(PaymentInstrument.fromJson(jsonPaymentInstrument));

      mockFindOne.mockResolvedValueOnce({
        id: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        email: "<EMAIL>",
      });

      mockFindOne.mockResolvedValueOnce(
        PaymentInstrument.fromJson({ ...jsonPaymentInstrument, verificationTransactionId: "1234" }),
      );
    });

    afterEach(() => {
      mockFindOne.mockReset();
      PayerAuthenticationApiMock.mockReset();
    });

    it("should return the payment instrument", async () => {
      await service.receiveTransaction("1234", "5678", userId);

      expect(createPaymentMock).toHaveBeenCalledWith(
        {
          clientReferenceInformation: {
            code: "GLOBAL_PAYMENT_CLIENT_REFERENCE_CODE",
          },
          consumerAuthenticationInformation: {
            authenticationTransactionId: "1234",
          },
          orderInformation: {
            amountDetails: {
              currency: "HKD",
              totalAmount: "0",
            },
          },
          paymentInformation: {
            paymentInstrument: {
              id: "Token",
            },
          },
          processingInformation: {
            actionList: ["VALIDATE_CONSUMER_AUTHENTICATION"],
            capture: false,
          },
          merchantInformation: {
            merchantDescriptor: { name: PAYMENT_MERCHANT_INFORMATION_MERCHANT_DESCRIPTOR_AUTH },
          },
        },
        expect.any(Function),
      );
    });

    it("Should throw an error when the user is not found", async () => {
      mockFindOne.mockReset();
      mockFindOne.mockResolvedValueOnce(null);

      await expect(service.receiveTransaction("1234", "5678", userId)).rejects.toThrow(
        errorBuilder.user.notFoundInSql(userId),
      );
    });

    it("Should throw an error when the payment instrument is not found", async () => {
      mockFindOne.mockReset();
      mockFindOne.mockResolvedValueOnce({
        id: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        email: "",
      });

      mockFindOne.mockResolvedValueOnce(null);

      await expect(service.receiveTransaction("1234", "5678", userId)).rejects.toThrow(
        errorBuilder.payment.instrument.notFound("1234"),
      );
    });

    it("Should throw an error when the verify user is not found", async () => {
      mockFindOne.mockReset();
      mockFindOne.mockResolvedValueOnce({
        id: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        email: "",
      });

      mockFindOne.mockResolvedValueOnce(PaymentInstrument.fromJson(jsonPaymentInstrument));

      mockFindOne.mockResolvedValueOnce(null);

      await expect(service.receiveTransaction("1234", "5678", userId)).rejects.toThrow(
        errorBuilder.user.notFoundInSql(userId),
      );
    });

    it("Should throw an error when the verify payment instrument is not found", async () => {
      mockFindOne.mockReset();
      mockFindOne.mockResolvedValueOnce({
        id: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        email: "",
      });

      mockFindOne.mockResolvedValueOnce(PaymentInstrument.fromJson(jsonPaymentInstrument));

      mockFindOne.mockResolvedValueOnce({
        id: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        email: "",
      });

      mockFindOne.mockResolvedValueOnce(null);

      await expect(service.receiveTransaction("1234", "5678", userId)).rejects.toThrow(
        errorBuilder.payment.instrument.notFound("1234"),
      );
    });
  });

  describe("verifyPaymentInstrument", () => {
    beforeEach(() => {
      mockFindOne.mockResolvedValueOnce({
        id: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        email: "<EMAIL>",
      });

      mockFindOne.mockResolvedValueOnce(
        PaymentInstrument.fromJson({ ...jsonPaymentInstrument, verificationTransactionId: "1234" }),
      );
    });

    it("should return the payment instrument", async () => {
      mockCount.mockResolvedValueOnce(2);

      const result = await service.verifyPaymentInstrument("1234", userId);

      expect(result).toEqual({
        ...defaultPaymentInstrument,
        state: PaymentInstrumentState.VERIFIED,
        verificationTransactionId: "1234",
        verifiedAt: expect.any(Date),
      });

      expect(createPaymentMock).toHaveBeenCalledWith(
        {
          clientReferenceInformation: {
            code: "GLOBAL_PAYMENT_CLIENT_REFERENCE_CODE",
          },
          consumerAuthenticationInformation: {
            authenticationTransactionId: "1234",
          },
          orderInformation: {
            amountDetails: {
              currency: "HKD",
              totalAmount: "0",
            },
          },
          paymentInformation: {
            paymentInstrument: {
              id: "Token",
            },
          },
          processingInformation: {
            actionList: ["VALIDATE_CONSUMER_AUTHENTICATION"],
            capture: false,
          },
          merchantInformation: {
            merchantDescriptor: { name: PAYMENT_MERCHANT_INFORMATION_MERCHANT_DESCRIPTOR_AUTH },
          },
        },
        expect.any(Function),
      );

      expect(mockCount).toHaveBeenCalledWith({
        where: { user: { id: userId }, state: PaymentInstrumentState.VERIFIED },
      });

      expect(mockSave).toHaveBeenCalledWith({
        ...defaultPaymentInstrument,
        isPreferred: false,
        state: PaymentInstrumentState.VERIFIED,
        verificationTransactionId: "1234",
        verifiedAt: expect.any(Date),
      });
    });

    it("should return the payment instrument with prefered true when it is the first one", async () => {
      mockCount.mockResolvedValueOnce(0);
      const result = await service.verifyPaymentInstrument("1234", userId);

      expect(result).toEqual({
        ...defaultPaymentInstrument,
        isPreferred: true,
        state: PaymentInstrumentState.VERIFIED,
        verificationTransactionId: "1234",
        verifiedAt: expect.any(Date),
      });

      expect(createPaymentMock).toHaveBeenCalledWith(
        {
          clientReferenceInformation: {
            code: "GLOBAL_PAYMENT_CLIENT_REFERENCE_CODE",
          },
          consumerAuthenticationInformation: {
            authenticationTransactionId: "1234",
          },
          orderInformation: {
            amountDetails: {
              currency: "HKD",
              totalAmount: "0",
            },
          },
          paymentInformation: {
            paymentInstrument: {
              id: "Token",
            },
          },
          processingInformation: {
            actionList: ["VALIDATE_CONSUMER_AUTHENTICATION"],
            capture: false,
          },
          merchantInformation: {
            merchantDescriptor: { name: PAYMENT_MERCHANT_INFORMATION_MERCHANT_DESCRIPTOR_AUTH },
          },
        },
        expect.any(Function),
      );

      expect(mockCount).toHaveBeenCalledWith({
        where: { user: { id: userId }, state: PaymentInstrumentState.VERIFIED },
      });

      expect(mockSave).toHaveBeenCalledWith({
        ...defaultPaymentInstrument,
        isPreferred: true,
        state: PaymentInstrumentState.VERIFIED,
        verificationTransactionId: "1234",
        verifiedAt: expect.any(Date),
      });
    });

    it("Should throw an error when the user is not found", async () => {
      mockFindOne.mockReset();
      mockFindOne.mockResolvedValueOnce(null);

      await expect(service.verifyPaymentInstrument("1234", userId)).rejects.toThrow(
        errorBuilder.user.notFoundInSql(userId),
      );
    });

    it("Should throw an error when the payment instrument is not found", async () => {
      mockFindOne.mockReset();
      mockFindOne.mockResolvedValueOnce({
        id: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        email: "",
      });

      mockFindOne.mockResolvedValueOnce(null);

      await expect(service.verifyPaymentInstrument("1234", userId)).rejects.toThrow(
        errorBuilder.payment.instrument.notFound("1234"),
      );
    });
  });

  describe("checkPayerAuthEnrollment", () => {
    const checkPayerAuthEnrollmentMock = jest.fn();

    beforeEach(() => {
      checkPayerAuthEnrollmentMock.mockImplementation((...args: any[]) =>
        /// get last arg
        args[args.length - 1](null, {
          id: "id",
          submitTimeUtc: new Date().toISOString(),
          status: "AUTHENTICATION_SUCCESSFUL",
          clientReferenceInformation: "ref",
          consumerAuthenticationInformation: {},
        }),
      );
      PayerAuthenticationApiMock.mockImplementation(() => {
        return {
          payerAuthSetup: bindingMock(promisifyMock),
          validateAuthenticationResults: bindingMock(promisifyMock),
          checkPayerAuthEnrollment: bindingMock(checkPayerAuthEnrollmentMock),
        };
      });

      mockFindOne.mockResolvedValueOnce({
        id: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        email: "<EMAIL>",
        phoneNumber: "+85212345678",
      });

      mockFindOne.mockResolvedValueOnce(
        PaymentInstrument.fromJson({ ...jsonPaymentInstrument, verificationTransactionId: "1234" }),
      );
    });

    it("should return the payment instrument", async () => {
      const result = await service.checkPayerAuthEnrollment({ paymentInstrumentId: "1234" }, userId);

      expect(result).toEqual({
        ...jsonPaymentInstrument,
        id: expect.stringMatching(expects.uuid),
        toJson: expect.any(Function),
        verificationTransactionId: "1234",
      });

      expect(checkPayerAuthEnrollmentMock).toHaveBeenCalledWith(
        {
          clientReferenceInformation: {
            code: "GLOBAL_PAYMENT_CLIENT_REFERENCE_CODE",
          },
          orderInformation: {
            amountDetails: {
              currency: "HKD",
              totalAmount: "0",
            },
          },
          paymentInformation: {
            customer: {
              customerId: "Token",
            },
          },
        },
        expect.any(Function),
      );

      expect(mockSave).toHaveBeenCalledTimes(2);
      expect(mockSave).toHaveBeenNthCalledWith(1, {
        cardHolderName: "John Doe",
        cardPrefix: "412345",
        cardSuffix: "3456",
        cardType: "VISA",
        expirationDate: expect.any(Date),
        expirationMonth: "12",
        expirationYear: "2031",
        id: expect.stringMatching(expects.uuid),
        instrumentIdentifier: "1234",
        isPayerAuthEnroled: true,
        isPreferred: false,
        paymentGateway: "GLOBAL_PAYMENTS",
        state: "ACTIVE",
        toJson: expect.any(Function),
        token: "Token",
        updateExpirationDate: expect.any(Function),
        user: {
          id: "IkCO7YHOJ3c073P8RihpRZxX3yC2",
        },
        verificationTransactionId: "1234",
        verifiedAt: undefined,
      });
      expect(mockSave).toHaveBeenNthCalledWith(2, {
        id: "1234",
        metadata: {
          checkPayerAuthEnrollment: {
            clientReferenceInformation: "ref",
            consumerAuthenticationInformation: {},
            id: "id",
            status: "AUTHENTICATION_SUCCESSFUL",
            submitTimeUtc: expect.any(String),
          },
          isPayerAuthEnroled: true,
        },
        txApp: { id: "1234", name: "vis-mobility" },
      });
    });

    it("should throw an error when the user is not found", async () => {
      mockFindOne.mockReset();
      mockFindOne.mockResolvedValueOnce(null);

      mockFindOne.mockResolvedValueOnce(
        PaymentInstrument.fromJson({ ...jsonPaymentInstrument, verificationTransactionId: "1234" }),
      );

      await expect(service.checkPayerAuthEnrollment({ paymentInstrumentId: "1234" }, userId)).rejects.toThrow(
        errorBuilder.user.notFoundInSql(userId),
      );
    });

    it("should throw an error when the payment instrument is not found", async () => {
      mockFindOne.mockReset();
      mockFindOne.mockResolvedValueOnce({
        id: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        email: "",
      });

      mockFindOne.mockResolvedValueOnce(null);

      await expect(service.checkPayerAuthEnrollment({ paymentInstrumentId: "1234" }, userId)).rejects.toThrow(
        errorBuilder.payment.instrument.notFound("1234"),
      );
    });
  });
});
