import { Inject, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { CloudEvent } from "firebase-functions/v2";
import { MessagePublishedData } from "firebase-functions/v2/pubsub";
import moment from "moment";
import { Brackets, LessThanOrEqual, MoreThanOrEqual, SelectQueryBuilder } from "typeorm";

import { CreateCampaignBodyDto } from "./dto/createCampaignRequest.dto";
import { CampaignListingResponseDto, QueryCampaignRequestDto } from "./dto/queryCampaignRequest.dto";
import { UpdateCampaignBodyDto } from "./dto/updateCampaignRequest.dto";
import Campaign from "../database/entities/campaign.entity";
import Discount from "../database/entities/discount.entity";
import Tx from "../database/entities/tx.entity";
import User from "../database/entities/user.entity";
import { CampaignRepository } from "../database/repositories/campaign.repository";
import { DiscountRepository } from "../database/repositories/discount.repository";
import { DiscountEntity } from "../discount/discount.entity";
import { RedeemDiscountResponseDto, redeemDiscountResponseMessage } from "../discount/dto/redeem-discount.dto";
import { UserService } from "../user/user.service";
import { DashError, errorBuilder } from "../utils/utils/error.utils";
import { UtilsService } from "../utils/utils.service";
import { CampaignEventTriggers, CampaignSponsorType, ApplicationRuleParams } from "./dto/campaign.dto";
import { UserRepository } from "../database/repositories/user.repository";
import { DiscountState, TxDiscounts } from "../discount/dto/discount.dto";
import { JsonLogicService } from "../jsonLogic/jsonLogic.service";
import { CampaignGetIssuedResponse } from "../me/modules/meCampaign/dto/meCampaign.dto";
import { PublishMessageForCampaignTriggerProcessingParams } from "../pubsub/dto/publishMessageForCampaignTriggerProcessingParams";
import { TxTypes } from "../transaction/dto/txType.dto";
import LoggerServiceAdapter from "../utils/logger/logger.service";

@Injectable()
export class CampaignService {
  constructor(
    @InjectRepository(CampaignRepository) private campaignRepository: CampaignRepository,
    @InjectRepository(UserRepository) private readonly userRepository: UserRepository,
    @InjectRepository(DiscountRepository) private readonly discountRepository: DiscountRepository,
    @Inject(UtilsService) private readonly utilsService: UtilsService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    @Inject(UserService) private readonly userService: UserService,
    @Inject(JsonLogicService) private readonly jsonLogicService: JsonLogicService,
  ) {}

  /**
   * Create campaign
   * @param createCampaignBodyDto CreateCampaignBodyDto
   */
  async createCampaign(createCampaignBodyDto: CreateCampaignBodyDto): Promise<Campaign> {
    return this.campaignRepository.save(createCampaignBodyDto);
  }

  /**
   * Update campaign
   * @param campaignId string
   * @param updateCampaignBodyDto UpdateCampaignBodyDto
   */
  async updateCampaign(campaignId: string, updateCampaignBodyDto: UpdateCampaignBodyDto): Promise<Campaign> {
    const campaign = await this.campaignRepository.findOne({ where: { id: campaignId } });
    if (!campaign) {
      throw errorBuilder.incentive.campaign.notFound(campaignId);
    }
    const campaignToBeUpdated = { ...campaign, ...updateCampaignBodyDto };
    if (campaignToBeUpdated.endAt <= campaignToBeUpdated.startAt) {
      throw errorBuilder.incentive.campaign.invalidDate(campaignId);
    }
    return this.campaignRepository.save(campaignToBeUpdated);
  }

  /**
   * Query campaigns
   * @param queryCampaignRequestDto QueryCampaignRequestDto
   * @returns Promise<CampaignListingResponseDto>
   */
  async queryCampaigns(queryCampaignRequestDto: QueryCampaignRequestDto): Promise<CampaignListingResponseDto> {
    const alias = "campaign";
    let where = "1=1";
    if (queryCampaignRequestDto.startAt) {
      where += ` AND ${alias}.startAt >= to_timestamp('${new Date(
        queryCampaignRequestDto.startAt,
      ).toISOString()}', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')`;
    }
    if (queryCampaignRequestDto.endAt) {
      where += ` AND ${alias}.endAt <= to_timestamp('${new Date(
        queryCampaignRequestDto.endAt,
      ).toISOString()}', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')`;
    }
    const [campaigns, count] = await this.utilsService.api.paginateRaw<Campaign>(
      this.campaignRepository,
      alias,
      where,
      queryCampaignRequestDto,
      [],
      [],
      [`${alias}.id`],
      ["campaign.*"],
      true,
    );
    return { count, campaigns };
  }

  /**
   * Filter campaign
   */
  async filterCampaignByApplicationRules(ruleParams: ApplicationRuleParams): Promise<Campaign | undefined> {
    let queryAvailableCampaigns: Campaign[];
    const now = new Date();
    let foundCampaign: Campaign | undefined = undefined;
    if (ruleParams.userId) {
      queryAvailableCampaigns = await this.campaignRepository
        .createQueryBuilder("campaign")
        .leftJoinAndSelect("campaign.discounts", "discount")
        .where("campaign.endAt >= :now", { now })
        .andWhere("campaign.startAt <= :now", { now })
        .andWhere(
          new Brackets((qb) => {
            qb.where((qb1: SelectQueryBuilder<Campaign>) => {
              // Campaign has not reached maxCount
              const subQuery1 = qb1
                .subQuery()
                .select("COUNT(*)")
                .from("discount", "discount")
                .where("discount.userId = :userId", { userId: ruleParams.userId })
                .andWhere("discount.campaignId = campaign.id")
                .andWhere("discount.state = :state", { state: DiscountState.REDEEMED })
                .getQuery();
              return `(${subQuery1}) < campaign.maxCount`;
            })
              .andWhere((qb1: SelectQueryBuilder<Campaign>) => {
                // Campaign has not reached totalMaxCount
                const subQuery2 = qb1
                  .subQuery()
                  .select("COUNT(*)")
                  .from("discount", "discount")
                  .where("discount.campaignId = campaign.id")
                  .andWhere("discount.state IN (:...states)", {
                    states: [DiscountState.APPLIED, DiscountState.REDEEMED],
                  })
                  .getQuery();
                return `(${subQuery2}) < campaign.totalMaxCount`;
              })
              .orWhere((qb1: SelectQueryBuilder<Campaign>) => {
                // OR user has a discount issued for the campaign
                const existsSubQuery = qb1
                  .subQuery()
                  .select("1")
                  .from("discount", "discount")
                  .where("discount.campaignId = campaign.id")
                  .andWhere("discount.userId = :userId", { userId: ruleParams.userId })
                  .andWhere("discount.state = :state", { state: DiscountState.ISSUED })
                  .getQuery();
                return `EXISTS(${existsSubQuery})`;
              });
          }),
        )
        .orderBy("campaign.priority", "DESC")
        .getMany();
    } else {
      // If no user is provided, only consider onDemand campaigns
      queryAvailableCampaigns = await this.campaignRepository
        .createQueryBuilder("campaign")
        .leftJoinAndSelect("campaign.discounts", "discount")
        .where("campaign.endAt >= :now", { now })
        .andWhere("campaign.startAt <= :now", { now })
        .andWhere("campaign.isOnDemand = true")
        .andWhere((qb: SelectQueryBuilder<Campaign>) => {
          // Campaign has not reached totalMaxCount
          const subQuery = qb
            .subQuery()
            .select("COUNT(*)")
            .from("discount", "discount")
            .where("discount.campaignId = campaign.id")
            .andWhere("discount.state IN (:...states)", {
              states: [DiscountState.APPLIED, DiscountState.REDEEMED],
            })
            .getQuery();
          return `(${subQuery}) < campaign.totalMaxCount`;
        })
        .orderBy("campaign.priority", "DESC")
        .getMany();
    }
    this.logger.info(
      "queryAvailableCampaigns",
      queryAvailableCampaigns.map((campaign) => campaign.id),
    );
    if (queryAvailableCampaigns && queryAvailableCampaigns.length > 0) {
      for (const campaign of queryAvailableCampaigns) {
        const applicable = this.jsonLogicService.apply(JSON.parse(campaign.applicationRules), ruleParams.toJson());
        if (applicable) {
          foundCampaign = campaign;
          break;
        }
      }
    }
    return foundCampaign;
  }

  /**
   * Get campaign for both sponsor types by application rule
   * Returns a tuple of thirdPartyCampaign, dashCampaign
   */
  async getApplicableCampaigns(rule: ApplicationRuleParams): Promise<[Campaign | undefined, Campaign | undefined]> {
    const thirdPartyCampaign = await this.getApplicableCampaign(rule, CampaignSponsorType.THIRD_PARTY);
    const dashCampaign = await this.getApplicableCampaign(rule, CampaignSponsorType.DASH);

    return [thirdPartyCampaign, dashCampaign];
  }

  /**
   * Get campaign by application rule and sponsor type
   */
  async getApplicableCampaign(rule: ApplicationRuleParams, type: CampaignSponsorType): Promise<Campaign | undefined> {
    let queryAvailableCampaigns: Campaign[];
    const now = new Date();
    let foundCampaign: Campaign | undefined = undefined;
    if (rule.userId) {
      queryAvailableCampaigns = await this.campaignRepository
        .createQueryBuilder("campaign")
        .leftJoinAndSelect("campaign.discounts", "discount", "discount.userId = :userId", { userId: rule.userId })
        // Within campaign period and is of the correct sponsor type
        .where("campaign.endAt >= :now", { now })
        .andWhere("campaign.startAt <= :now", { now })
        .andWhere("campaign.sponsorType = :type", { type })
        .andWhere(
          new Brackets((qb) => {
            qb.where(
              new Brackets((qb2) => {
                // Either campaign is onDemand and has not reached maxCount
                qb2.where("campaign.isOnDemand = true").andWhere((qb3: SelectQueryBuilder<Campaign>) => {
                  const subQuery = qb3
                    .subQuery()
                    .select("COUNT(*)")
                    .from(Discount, "discount")
                    .where("discount.userId = :userId", { userId: rule.userId })
                    .andWhere("discount.campaignId = campaign.id")
                    .andWhere("discount.state IN (:...states)", {
                      states: [DiscountState.ISSUED, DiscountState.APPLIED, DiscountState.REDEEMED],
                    })
                    .getQuery();
                  return `(${subQuery}) < campaign.maxCount`;
                });
              }),
            ).orWhere(
              new Brackets((qb4) => {
                // Or campaign is not onDemand and has issued discount
                qb4.where("campaign.isOnDemand = false").andWhere((qb5: SelectQueryBuilder<Campaign>) => {
                  const subQuery = qb5
                    .subQuery()
                    .select("COUNT(*)")
                    .from(Discount, "discount")
                    .where("discount.userId = :userId", { userId: rule.userId })
                    .andWhere("discount.campaignId = campaign.id")
                    .andWhere("discount.state = :stateIssued", { stateIssued: DiscountState.ISSUED })
                    .andWhere("discount.startAt <= :now", { now })
                    .andWhere("discount.endAt >= :now", { now })
                    .getQuery();
                  return `(${subQuery}) > 0`;
                });
              }),
            );
          }),
        )
        .orderBy("campaign.priority", "DESC")
        .getMany();
    } else {
      // For non-user specific queries, only consider onDemand campaigns
      queryAvailableCampaigns = await this.campaignRepository
        .createQueryBuilder("campaign")
        .where("campaign.endAt >= :now", { now })
        .andWhere("campaign.startAt <= :now", { now })
        .andWhere("campaign.sponsorType = :type", { type })
        .andWhere("campaign.isOnDemand = true")
        .orderBy("campaign.priority", "DESC")
        .getMany();
    }

    this.logger.debug(`[getApplicableCampaign] query: ${queryAvailableCampaigns.map((campaign) => campaign.id)}`);

    if (queryAvailableCampaigns && queryAvailableCampaigns.length > 0) {
      for (const campaign of queryAvailableCampaigns) {
        const applicable = this.jsonLogicService.apply(JSON.parse(campaign.applicationRules), {
          transactionType: rule.transactionType,
          transactionSubtype: rule.transactionSubtype,
          paymentInstrumentType: rule.paymentInstrumentType,
          paymentChannel: rule.paymentChannel,
        });
        if (applicable) {
          foundCampaign = campaign;
          break;
        }
      }
    }

    return foundCampaign;
  }

  async getCampaignsListWithAggregatedValuesForUser(user: User): Promise<CampaignGetIssuedResponse> {
    const now = new Date();

    const campaignsQuery = this.campaignRepository
      .createQueryBuilder("campaign")
      .select(["campaign"])
      .where((qb) => {
        const subQuery = qb
          .subQuery()
          .select("1")
          .from(Discount, "discount")
          .where("discount.campaignId = campaign.id")
          .andWhere("discount.user = :userId", { userId: user.id })
          .andWhere("discount.state != :state", { state: DiscountState.FAILED })
          .andWhere("discount.startAt <= :now", { now })
          // .andWhere("discount.endAt >= :now", { now })
          .getQuery();
        return `EXISTS ${subQuery}`;
      })
      .leftJoinAndSelect("campaign.discounts", "discount", "discount.user = :userId AND discount.state != :failed", {
        failed: DiscountState.FAILED,
        userId: user.id,
      })
      .leftJoinAndSelect("discount.tx", "tx");

    const redeemedValueQuery = this.campaignRepository
      .createQueryBuilder("campaign")
      .leftJoin("campaign.discounts", "discount")
      .where("discount.user = :userId", { userId: user.id })
      .andWhere("discount.state = :state", { state: DiscountState.REDEEMED })
      .select("SUM(discount.redeemedValue)", "totalRedeemedValue");

    const availableValueQuery = this.campaignRepository
      .createQueryBuilder("campaign")
      .leftJoin("campaign.discounts", "discount")
      .where("discount.userId = :userId", { userId: user.id })
      .andWhere("discount.state != :state", { state: DiscountState.FAILED })
      .select("SUM(campaign.availableValue)", "totalAvailableValue");

    const [campaigns, redeemedValue, availableValue] = await Promise.all([
      campaignsQuery.getMany(),
      redeemedValueQuery.getRawOne(),
      availableValueQuery.getRawOne(),
    ]);

    const totalRedeemedValue = redeemedValue?.totalRedeemedValue ? parseFloat(redeemedValue.totalRedeemedValue) : 0;

    const totalAvailableValue = availableValue?.totalAvailableValue
      ? parseFloat(availableValue.totalAvailableValue)
      : 0;

    return { campaigns, totalRedeemedValue, totalAvailableValue };
  }

  async getCampaignsListWithAggregatedValues(userId: string): Promise<CampaignGetIssuedResponse> {
    const foundUser = await this.userRepository.findOne({ where: { appDatabaseId: userId } });
    if (!foundUser) {
      throw errorBuilder.user.notFoundInSql(userId);
    }

    return this.getCampaignsListWithAggregatedValuesForUser(foundUser);
  }

  async applyCampaignByIdAndUserId(campaignId: string, tx: Tx, userId: string): Promise<Discount> {
    const campaign = await this.campaignRepository.findOne({ where: { id: campaignId } });

    if (!campaign) {
      throw errorBuilder.incentive.campaign.notFound(campaignId);
    }

    const user = await this.userRepository.findOne({ where: { appDatabaseId: userId } });

    if (!user) {
      throw errorBuilder.user.notFoundInSql(userId);
    }

    return this.applyCampaign(campaign, tx, user);
  }

  async applyCampaign(campaign: Campaign, tx?: Tx, user?: User): Promise<Discount> {
    if (campaign.isOnDemand) {
      return this.createOnDemandDiscount(campaign, tx, user);
    } else {
      if (!user) {
        throw errorBuilder.incentive.discount.userNotFound(campaign.id);
      }

      // Find issued discount with earliest end date
      const discount = await this.discountRepository.findOne({
        where: {
          campaign: { id: campaign.id },
          user: { id: user.id },
          state: DiscountState.ISSUED,
          startAt: LessThanOrEqual(new Date()),
          endAt: MoreThanOrEqual(new Date()),
        },
        order: { endAt: "ASC" },
        relations: ["campaign"],
      });

      if (!discount) {
        throw errorBuilder.incentive.discount.notFound(campaign.id);
      }

      discount.state = DiscountState.APPLIED;
      if (tx) {
        discount.tx = tx;
      }
      discount.user = user;

      const savedDiscount = await this.discountRepository.save(discount);

      return savedDiscount;
    }
  }

  /**
   * Apply discount to tx
   */
  async applyDiscountToTx(discountId: string, tx: Tx): Promise<boolean> {
    const result = await this.discountRepository.update(discountId, { tx: tx });
    if (result.affected === 0) {
      return false;
    }
    return true;
  }

  /**
   *
   * Apply discount to tx, with custom redeem value
   */
  async applyDiscountToTxWithCustomValue(
    discountId: string,
    redeemValue: number,
    tx: Tx,
  ): Promise<RedeemDiscountResponseDto> {
    const discount = await this.discountRepository.findOne({
      where: { id: discountId },
      relations: { campaign: true },
      loadEagerRelations: true,
    });
    if (!discount) {
      throw errorBuilder.incentive.discount.notFound(discountId);
    }

    // FIXME: use state machine to handle transitions
    // other discount states may still be acceptable
    if (discount.state === DiscountState.REDEEMED) {
      throw errorBuilder.incentive.discount.alreadyRedeemed(discountId);
    }

    if (discount.user?.id !== tx.user?.id) {
      throw errorBuilder.incentive.discount.userMismatch(discountId, tx.id, discount.user?.id, tx.user?.id);
    }

    // we cannot redeem a discount that exceeds the discount value
    const discountRedeemableValue = discount.campaign.availableValue ?? 0;
    if (redeemValue > discountRedeemableValue) {
      throw errorBuilder.incentive.discount.valueExceeded(discountId, discountRedeemableValue);
    }

    // make sure that the tx type is valid
    if (![TxTypes.TRIP, TxTypes.HAILING_REQUEST].includes(tx.type)) {
      throw errorBuilder.incentive.discount.invalidTxType(tx.id, tx.type);
    }

    // update discount as redeemed, and date was the last trip end date
    const result = await this.discountRepository.update(discountId, {
      tx,
      state: DiscountState.REDEEMED,
      redeemedValue: redeemValue,
      redeemedAt: new Date(),
    });
    if (result.affected === 0) {
      throw errorBuilder.incentive.discount.notApplied(discountId);
    }

    return redeemDiscountResponseMessage;
  }

  /**
   * Create discount instance on demand
   */
  async createOnDemandDiscount(campaign: Campaign, tx?: Tx, user?: User): Promise<Discount> {
    const discount = new Discount();
    discount.campaign = campaign;
    discount.state = DiscountState.APPLIED;
    if (tx) {
      discount.tx = tx;
    }
    discount.user = user;
    const savedDiscount = await this.discountRepository.save(discount);

    return savedDiscount;
  }

  async createIssuedDiscountById(campaignId: string, userId: string): Promise<DiscountEntity> {
    const campaign = await this.getCampaignById(campaignId);
    if (!campaign) {
      throw errorBuilder.incentive.campaign.notFound(campaignId);
    }

    const now = new Date();

    if (now < campaign.startAt) {
      throw errorBuilder.incentive.campaign.notYetStarted(campaignId);
    }

    if (now > campaign.endAt) {
      throw errorBuilder.incentive.campaign.alreadyEnded(campaignId);
    }

    const user = await this.userService.getUserById(userId);
    if (!user) {
      throw errorBuilder.user.notFoundInSql(userId);
    }

    try {
      const discount = await this.createIssuedDiscount(campaign, user);
      if (!discount) {
        throw errorBuilder.incentive.discount.unableToCreate();
      }

      return new DiscountEntity(discount);
    } catch (error) {
      if (error instanceof DashError) {
        throw error;
      }

      this.logger.error("[createIssuedDiscountById] failed to create issued discount", {
        campaignId,
        userId,
        error,
      });

      throw errorBuilder.global.unexpected(error);
    }
  }

  /**
   * Create discount instance for non on demand
   */
  async createIssuedDiscount(campaign: Campaign, user?: User): Promise<Discount> {
    const discount = new Discount();
    discount.campaign = campaign;
    discount.state = DiscountState.ISSUED;
    discount.user = user;
    if (campaign.expiryRules) {
      discount.startAt = new Date();
      try {
        const duration = this.utilsService.date.parseDuration(campaign.expiryRules);
        const endAt = moment(discount.startAt).add(duration).toDate();

        discount.endAt = endAt;
      } catch (e) {
        this.logger.error("[createIssuedDiscount] failed to parse expiry rules", {
          campaignId: campaign.id,
          expiryRules: campaign.expiryRules,
          error: e,
        });
        discount.endAt = campaign.endAt;
      }
    } else {
      discount.startAt = campaign.startAt;
      discount.endAt = campaign.endAt;
    }
    const savedDiscount = await this.discountRepository.save(discount);

    return savedDiscount;
  }

  /**
   * Redeem tx discounts
   */
  async redeemDiscounts(discounts: TxDiscounts): Promise<void> {
    const now = new Date();
    if (discounts.thirdParty) {
      await this.redeemDiscountById(discounts.thirdParty.id, now);
    }
    if (discounts.dash) {
      await this.redeemDiscountById(discounts.dash.id, now);
    }
  }

  /**
   * Redeem discount
   */
  async redeemDiscountById(discountId: string, redeemedAt: Date): Promise<void> {
    await this.discountRepository.update(discountId, { state: DiscountState.REDEEMED, redeemedAt });
  }

  /**
   * Set tx discounts as failed or return them back to issued
   */
  async resetOrFailDiscounts(discounts: TxDiscounts): Promise<void> {
    if (discounts.thirdParty) {
      await this.resetOrFailDiscount(discounts.thirdParty);
    }
    if (discounts.dash) {
      await this.resetOrFailDiscount(discounts.dash);
    }
  }

  /**
   * Set discount as failed or return it back to issued
   */
  async resetOrFailDiscount(discount: Discount): Promise<void> {
    this.logger.debug("[resetOrFailDiscount] discount for user", {
      discountId: discount.id,
      userId: discount.user?.id,
      onDemand: discount.campaign.isOnDemand,
    });
    if (!discount.campaign.isOnDemand && discount.user) {
      await this.discountRepository.update(discount.id, {
        state: DiscountState.ISSUED,
        tx: null,
        redeemedValue: null,
      });
    } else {
      await this.discountRepository.update(discount.id, { state: DiscountState.FAILED });
    }
  }

  /**
   * Reset or fail discount by id
   */
  async resetOrFailDiscountById(discountId: string): Promise<void> {
    const discount = await this.discountRepository.findOne({
      where: { id: discountId },
      relations: ["campaign", "user"],
    });
    if (!discount) {
      throw errorBuilder.incentive.discount.notFound(discountId);
    }
    await this.resetOrFailDiscount(discount);
  }

  /**
   * Find discounts for sponsor types
   */
  async getTxDiscounts(tx: Tx): Promise<TxDiscounts> {
    const discounts = await this.discountRepository.find({
      where: { tx: { id: tx.id }, state: DiscountState.APPLIED },
      relations: ["campaign", "user"],
    });
    const thirdPartyDiscount = discounts.find(
      (discount) => discount.campaign?.sponsorType === CampaignSponsorType.THIRD_PARTY,
    );
    const dashDiscount = discounts.find((discount) => discount.campaign?.sponsorType === CampaignSponsorType.DASH);

    return { thirdParty: thirdPartyDiscount, dash: dashDiscount };
  }

  /**
   * Set discount redeem value
   */
  async setDiscountRedeemedValue(discount: Discount, redeemedValue: number): Promise<void> {
    await this.discountRepository.update(discount.id, { redeemedValue });
  }

  async processCampaignTrigger(
    params: CloudEvent<MessagePublishedData<PublishMessageForCampaignTriggerProcessingParams>>,
  ) {
    const data = params.data.message.json;
    this.logger.info("[processCampaignTrigger] processing", { data });

    // Fetch campaign where eventTriggers contains eventTrigger
    const eventTrigger = data.eventTrigger;
    const userId = data.userId;
    const generateToLimit = data.generateToLimit ?? false;

    await this._issueDiscountForEventTrigger(eventTrigger, userId, generateToLimit);
  }

  /**
   * Issue discount for event trigger
   */
  async _issueDiscountForEventTrigger(eventTrigger: string, userId: string, generateToLimit: boolean = false) {
    const foundUser = await this.userRepository.findOne({ where: { id: userId }, relations: ["referrer"] });
    if (!foundUser) {
      throw errorBuilder.user.notFoundInSql(userId);
    }
    if (eventTrigger === CampaignEventTriggers.USER_SIGNED_UP_REFEREE && !foundUser.referrer) {
      this.logger.debug("[processCampaignTrigger] no referrer found for user", { userId });
      return;
    }

    const now = new Date();

    let query = this.campaignRepository
      .createQueryBuilder("campaign")
      .where("campaign.eventTriggers @> ARRAY[:eventTrigger]", { eventTrigger })
      .andWhere("campaign.startAt <= :now", { now })
      .andWhere("campaign.endAt >= :now", { now })
      .andWhere("campaign.isOnDemand = false")
      .orderBy("campaign.priority", "DESC");

    // Filter only campaigns with available total max count
    query = query.andWhere((qb) => {
      const subQuery = qb
        .subQuery()
        .select("COUNT(discount.id)")
        .from(Discount, "discount")
        .where("discount.campaignId = campaign.id")
        .andWhere("discount.state IN (:...states)", {
          states: [DiscountState.ISSUED, DiscountState.APPLIED, DiscountState.REDEEMED],
        })
        .getQuery();

      return `(${subQuery}) < campaign.campaignMaxCount`;
    });

    // Filter only campaigns with available max count
    query = query.andWhere((qb) => {
      const subQuery = qb
        .subQuery()
        .select("COUNT(discount.id)")
        .from(Discount, "discount")
        .where("discount.campaignId = campaign.id")
        .andWhere("discount.userId = :userId", { userId })
        .andWhere("discount.state IN (:...states)", {
          states: [DiscountState.ISSUED, DiscountState.APPLIED, DiscountState.REDEEMED],
        })
        .getQuery();

      return `(${subQuery}) < campaign.maxCount`;
    });

    const campaigns = await query.getMany();

    if (campaigns && campaigns.length > 0) {
      for (const campaign of campaigns) {
        // Create discount instance(s)
        if (generateToLimit) {
          const count = await this.discountRepository.count({
            where: { campaign: { id: campaign.id }, user: { id: foundUser.id } },
          });
          for (let i = count; i < campaign.maxCount; i++) {
            await this.createIssuedDiscount(campaign, foundUser);
          }
          this.logger.info("[processCampaignTrigger] created discounts for user", {
            userId: foundUser.id,
            number: campaign.maxCount - count,
            campaignId: campaign.id,
          });
        } else {
          const discount = await this.createIssuedDiscount(campaign, foundUser);
          this.logger.info("[processCampaignTrigger] created discount for user", {
            userId: foundUser.id,
            discountId: discount.id,
            campaignId: campaign.id,
          });
        }
      }

      // Special handling for referral
      // Assume referral campaign max count is 1 so if a discount is created, it means the referrer bonus should also be created
      if (eventTrigger === CampaignEventTriggers.USER_SIGNED_UP_REFEREE) {
        await this._issueDiscountForReferrer(foundUser, eventTrigger);
      }
    } else {
      this.logger.info("[processCampaignTrigger] no campaign found for eventTrigger", { eventTrigger });
    }
  }

  /**
   * Issue discount for referrer, assumes referral campaign max count is 1, so referrer bonus should also be created
   */
  async _issueDiscountForReferrer(referral: User, eventTrigger: string) {
    // Find referrer
    const referrer = await referral.referrer;
    if (!referrer) {
      this.logger.info("[_issueDiscountForReferrer] No referrer found for user", { userId: referral.id });
      return;
    }

    if (eventTrigger === CampaignEventTriggers.USER_SIGNED_UP_REFERRER) {
      this.logger.error("[_issueDiscountForReferrer] Called with USER_SIGNED_UP_REFERRER", { userId: referral.id });
      return;
    }

    await this._issueDiscountForEventTrigger(CampaignEventTriggers.USER_SIGNED_UP_REFERRER, referrer.id);
  }

  /**
   * Get campaign by id
   */
  async getCampaignById(campaignId?: string): Promise<Campaign | null> {
    if (!campaignId) {
      return null;
    }
    return this.campaignRepository.findOne({
      where: { id: campaignId },
    });
  }

  /**
   * Claim a discount by reward code
   */
  async claimDiscount(rewardCode: string, userId: string): Promise<Discount> {
    const foundUser = await this.userRepository.findOne({ where: { appDatabaseId: userId } });
    if (!foundUser) {
      throw errorBuilder.user.notFoundInSql(userId);
    }

    const discount = await this.discountRepository.findOne({
      where: { rewardCode, state: DiscountState.ISSUED },
      relations: ["campaign", "user"],
    });

    if (!discount) {
      throw errorBuilder.incentive.discount.rewardCodeNotFound(rewardCode);
    }

    if (discount.user) {
      throw errorBuilder.incentive.discount.rewardCodeAlreadyClaimed(rewardCode);
    }

    if (discount.endAt && discount.endAt < new Date()) {
      throw errorBuilder.incentive.discount.rewardCodeExpired(rewardCode);
    }

    const count = await this.discountRepository.count({
      where: { campaign: { id: discount.campaign.id }, user: { id: foundUser.id } },
    });

    if (count >= discount.campaign.maxCount) {
      throw errorBuilder.incentive.discount.rewardCodeLimitExceeded(discount.campaign.id);
    }

    discount.user = foundUser;

    const savedDiscount = await this.discountRepository.save(discount);

    return savedDiscount;
  }
}
