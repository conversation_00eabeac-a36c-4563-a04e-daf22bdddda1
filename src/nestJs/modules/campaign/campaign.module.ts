import { forwardRef, <PERSON>dule } from "@nestjs/common";

import { CampaignService } from "./campaign.service";
import { CampaignRepository } from "../database/repositories/campaign.repository";
import { DiscountRepository } from "../database/repositories/discount.repository";
import { JsonLogicModule } from "../jsonLogic/jsonLogic.module";
import { UserModule } from "../user/user.module";

@Module({
  providers: [CampaignRepository, CampaignService, DiscountRepository],
  imports: [forwardRef(() => UserModule), JsonLogicModule],
  controllers: [],
  exports: [CampaignService],
})
export class CampaignModule {}
