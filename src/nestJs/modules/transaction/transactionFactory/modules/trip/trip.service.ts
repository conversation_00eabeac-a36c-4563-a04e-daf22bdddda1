import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { Inject, Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { InjectRepository } from "@nestjs/typeorm";
import { Cache } from "cache-manager";
import { Timestamp } from "firebase-admin/firestore";
import { Notification } from "firebase-admin/messaging";
import { forEach } from "lodash";
import moment from "moment";

import Payout from "@nest/modules/database/entities/payout.entity";
import { JsonLogicService } from "@nest/modules/jsonLogic/jsonLogic.service";
import { DeepPartial } from "@nest/types/types.utils";

import { TripDocumentReceipt, tripDocumentReceiptSchema } from "./dto/tripDocument.dto";
import { TripType } from "./types";
import { AppDatabaseService } from "../../../../appDatabase/appDatabase.service";
import {
  NotificationDocument,
  NotificationDocumentType,
  NotificationStatus,
} from "../../../../appDatabase/documents/driver.document";
import { LockDocument } from "../../../../appDatabase/documents/lock.document";
import { Location, Rating, TripDocument } from "../../../../appDatabase/documents/trip.document";
import { TxAppsNames } from "../../../../apps/dto/Apps.dto";
import { BankResponseLineItemStatus, PayoutBankFileRow } from "../../../../bank/dto/payoutBankFile.dto";
import { CampaignService } from "../../../../campaign/campaign.service";
import {
  ApplicationRuleParams,
  PaymentChannelType,
  PaymentInstrumentType,
} from "../../../../campaign/dto/campaign.dto";
import TxApp from "../../../../database/entities/app.entity";
import Discount from "../../../../database/entities/discount.entity";
import PaymentTx from "../../../../database/entities/paymentTx.entity";
import Tx from "../../../../database/entities/tx.entity";
import { TxAppRepository } from "../../../../database/repositories/app.repository";
import { DiscountRepository } from "../../../../database/repositories/discount.repository";
import { TxRepository } from "../../../../database/repositories/tx.repository";
import { UserRepository } from "../../../../database/repositories/user.repository";
import { UserNotificationTokenRepository } from "../../../../database/repositories/userNotificationToken.repository";
import { TxDiscounts, DiscountState } from "../../../../discount/dto/discount.dto";
import { FcmService } from "../../../../fcm/fcm.service";
import {
  ClickNotificationEventType,
  NotificationRecipientType,
  NotificationTriggerEventType,
} from "../../../../fcm/types";
import { PreferredLanguageType } from "../../../../identity/dto/user.dto";
import {
  LocalizedLanguage,
  LocationLanguage,
  LocationReverseGeocodeResponse,
} from "../../../../location/dto/location.dto";
import { LocationService } from "../../../../location/location.service";
import { ChannelTypes } from "../../../../message/dto/channelType.dto";
import { TemplateTypesText } from "../../../../message/dto/templateType.dto";
import {
  NotificationType,
  notificationTemplate,
} from "../../../../message/messageFactory/modules/notification/notification.dto";
import { PaymentGatewayTypes } from "../../../../payment/dto/paymentGatewayTypes.dto";
import { PaymentInformationStatus } from "../../../../payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../../../payment/dto/paymentInformationType.dto";
import { isPaymentMethodSelected } from "../../../../payment/dto/paymentMethodSelected.dto";
import { PaymentStatus } from "../../../../payment/dto/paymentStatus.dto";
import { PaymentTxReceipt, paymentTxReceiptSchema } from "../../../../payment/dto/paymentTx.dto";
import PaymentTxFromDocument from "../../../../payment/dto/paymentTxFromDocument.model";
import { PaymentType } from "../../../../payment/dto/paymentType.dto";
import { PaymentService } from "../../../../payment/payment.service";
import { PubSubService } from "../../../../pubsub/pubsub.service";
import LoggerServiceAdapter from "../../../../utils/logger/logger.service";
import casesUtils from "../../../../utils/utils/case/case.utils";
import { errorBuilder } from "../../../../utils/utils/error.utils";
import { calculateDashTransactionFee, roundOneDecimal, roundUpOneDecimal } from "../../../../utils/utils/number.utils";
import { UtilsService } from "../../../../utils/utils.service";
import { LanguageOption } from "../../../../validation/dto/language.dto";
import { ValidationService } from "../../../../validation/validation.service";
import { isTxAdjustment, isTxHailing, isTxTrip } from "../../../dto/tx.dto";
import { TxHailingMetadata } from "../../../dto/txMetadata.dto";
import { PaymentCardInformation, ReceiptLanguageType, TxReceipt, TxReceiptTrip } from "../../../dto/txReceipt.dto";
import { SubTxTypes, TxTypes } from "../../../dto/txType.dto";
import TransactionFactoryInterface from "../../transactionFactoryInterface";

/**
 * Trip service
 */
@Injectable()
export class TripService implements TransactionFactoryInterface {
  constructor(
    private pubsubService: PubSubService,
    private paymentService: PaymentService,
    private readonly configService: ConfigService,
    @Inject(UtilsService) private readonly utilsService: UtilsService,
    private readonly appDatabaseService: AppDatabaseService,
    @InjectRepository(TxRepository) private readonly txRepository: TxRepository,
    @InjectRepository(TxAppRepository) private readonly txAppRepository: TxAppRepository,
    @InjectRepository(DiscountRepository) private readonly discountRepository: DiscountRepository,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
    private fcmService: FcmService,
    @InjectRepository(UserRepository) private readonly userRepository: UserRepository,
    @InjectRepository(UserNotificationTokenRepository)
    private readonly userNotificationTokenRepository: UserNotificationTokenRepository,
    private readonly locationService: LocationService,
    private readonly campaignService: CampaignService,
    private readonly jsonLogicService: JsonLogicService,
  ) {}

  /**
   * Method where we check if the transaction has customer information, like phone number
   * @param tx Tx
   * @returns boolean
   */
  hasCustomerInformation(tx: Tx) {
    if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/hasCustomerInformation");
    }
    return !!tx.metadata.passengerInformation?.phone;
  }
  /**
   * check if the tx is pay with Dash
   * @param tx Tx
   * @returns boolean
   */
  isPayWithDash(tx: Tx) {
    if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/isPayWithDash");
    }
    const trip: TripDocument = tx.metadata;
    return trip.paymentType === PaymentType.DASH;
  }
  /**
   * get expiresAt for trip in driver in firestore
   * @param passedTx Tx
   * @returns Date
   */
  async getExpiresAt(passedTx: Tx): Promise<Date> {
    if (!isTxTrip(passedTx)) {
      throw errorBuilder.transaction.wrongImplement(passedTx.type, "TripService/getExpiresAt");
    }
    const trip: TripDocument = passedTx.metadata;
    const ttl =
      trip.paymentType === PaymentType.DASH
        ? await this.appDatabaseService.configurationRepository().getDashTripTTL()
        : await this.appDatabaseService.configurationRepository().getCashTripTTL();
    const duration = this.utilsService.date.parseDuration(ttl);
    const expiresAt = moment(trip.tripStart).add(duration).toDate();
    return expiresAt;
  }
  /**
   * Send event in pubsub when payment is captured successfully to send receipt to customer
   * @param paymentTx PaymentTx
   * @param trip TripDocument
   * @returns string
   */
  async sendReceipt(tx: Tx, paymentTx: PaymentTx): Promise<string> {
    if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/sendReceipt");
    }
    const trip: TripDocument = TripDocument.fromJson(tx.metadata, this.logger);
    const [paymentTxReceipt, tripDocumentReceipt] = ValidationService.validate<[PaymentTxReceipt, TripDocumentReceipt]>(
      [
        { value: paymentTx, schema: paymentTxReceiptSchema },
        { value: trip, schema: tripDocumentReceiptSchema },
      ],
    );
    return await this.pubsubService.publishMessageForMessageProcessingParams({
      metadata: {
        schemeVersion: tripDocumentReceipt.meterSoftwareVersion,
        createdAt: tripDocumentReceipt.lastUpdateTime,
      },
      recipient: {
        phone: tripDocumentReceipt.passengerInformation.phone,
      },
      tranId: paymentTxReceipt.gatewayTransactionId,
      channel: ChannelTypes.WHATSAPP,
      language: this.utilsService.language.getLanguageType(tripDocumentReceipt.language),
      params: {
        receiptLink: `${this.configService.getOrThrow("RECEIPT_URL")}/${tripDocumentReceipt.id}?lang=${
          tripDocumentReceipt.language === "en" ? ReceiptLanguageType.ENHK : ReceiptLanguageType.ZHHK
        }`,
        licensePlate: tripDocumentReceipt.licensePlate,
        tripTotal: paymentTxReceipt.amount,
        tripEndTime: moment(tripDocumentReceipt.tripEnd).tz("Asia/Hong_Kong").format("YYYY-MM-DD HH:mm:ss"),
      },
      messageId: paymentTxReceipt.id,
      template: TemplateTypesText.RECEIPT,
    });
  }

  /**
   * triggered on pre payment capture
   * @param tx Tx
   * @returns Promise<Tx>
   */
  async prePaymentProcess(tx: Tx): Promise<Tx> {
    if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/prePaymentProcess");
    }
    const trip: TripDocument = tx.metadata;

    if (!trip.tripEnd) {
      throw errorBuilder.transaction.trip.notEnded(tx.id);
    }

    return tx;
  }

  /**
   * get monetary info for tx
   * @param tx Tx
   * @returns Promise<Tx>
   */
  getTxMonetary(tx: Tx) {
    if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/getTxMonetary");
    }
    const trip: TripDocument = tx.metadata;

    const dashTransactionFee =
      trip.paymentType === PaymentType.DASH
        ? calculateDashTransactionFee({
            tripTotal: trip.tripTotal,
            dashTips: trip.dashTips,
            dashFeeRate: trip.dashFeeRate ?? trip.billing?.dashFeeSettings?.dashFeeRate,
            dashFeeConstant: trip.dashFeeConstant ?? trip.billing?.dashFeeSettings?.dashFeeConstant,
            additionalBookingFee: trip.billing?.additionalBookingFee,
            dashBookingFee: trip.billing?.dashBookingFee,
            fleetBookingFee: trip.billing?.fleetBookingFee,
            boostAmount: trip.billing?.boostAmount,
          })
        : 0;
    const dashFeeTotal = dashTransactionFee + (trip.billing?.dashBookingFee ?? 0);

    const total = roundOneDecimal(
      trip.tripTotal +
        (trip.dashTips ?? 0) +
        dashTransactionFee +
        (trip.billing?.additionalBookingFee ?? 0) +
        (trip.billing?.dashBookingFee ?? 0) +
        (trip.billing?.fleetBookingFee ?? 0) +
        (trip.billing?.boostAmount ?? 0),
    );

    let payoutAmount =
      trip.paymentType === PaymentType.DASH && trip.tripEnd
        ? roundOneDecimal(
            trip.tripTotal +
              (trip.dashTips ?? 0) +
              (trip.billing?.additionalBookingFee ?? 0) +
              (trip.billing?.fleetBookingFee ?? 0) +
              (trip.billing?.boostAmount ?? 0),
          )
        : 0;

    if (trip.billing?.fleetPayoutFee) {
      payoutAmount = trip.billing.fleetPayoutFee;
    }

    const billing = this.generateBillingObject(casesUtils.snakeKeys(trip));

    return { total, dashFee: dashFeeTotal, payoutAmount, billing };
  }

  /**
   * triggered on post payment capture
   * @param tx Tx
   * @returns Promise<Tx>
   */
  async postPaymentProcess(tx: Tx, success: boolean): Promise<Tx> {
    if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/postPaymentProcess");
    }
    const expiresAt = await this.getExpiresAt(tx);

    const trip: TripDocument = tx.metadata;
    trip.paymentStatus = success ? PaymentStatus.CAPTURED : PaymentStatus.FAILED;
    await this.txRepository.update(tx.id, { metadata: trip });

    await this.pubsubService.publishMessageForCopyTripToDriverProcessing({
      txId: tx.id,
      expiresAt: expiresAt,
    });
    return tx;
  }

  async shouldCopyTripToDriverTrip(tx: Tx, trip: TripDocument): Promise<boolean> {
    const driverId = trip.driver?.id;
    if (driverId) {
      const driverTrip = await this.appDatabaseService.driverTripRepository(driverId).findOneById(tx.id);
      if (!driverTrip) return true;
      const sessionId = trip.session?.id;
      if (sessionId) {
        const driverSessionTrip = await this.appDatabaseService
          .driverSessionTripRepository(driverId, sessionId)
          .findOneById(tx.id);
        if (!driverSessionTrip) return true;
      }
    }
    return false;
  }
  /**
   * triggered on post trip end
   * @param tx Tx
   * @returns Promise<Tx>
   */
  async postTxEndProcess(tx: Tx): Promise<Tx> {
    if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/postTxEndProcess");
    }

    const expiresAt = await this.getExpiresAt(tx);
    await this.pubsubService.publishMessageForCopyTripToDriverProcessing({
      txId: tx.id,
      expiresAt: expiresAt,
    });
    return tx;
  }
  /**
   * triggered on post cash payment capture
   * @param tx Tx
   * @returns Promise<Tx>
   */
  async postCashPaymentProcess(tx: Tx): Promise<Tx> {
    if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/postCashPaymentProcess");
    }
    const trip: TripDocument = tx.metadata;
    this.logger.debug("postCashPaymentProcess", {
      tx: tx.id,
      showCashTrip: tx.merchant?.showCashTrip,
      paymentType: trip.paymentType,
      typeOfPaymentType: typeof trip.paymentType,
    });
    //only when driver's show_cash_trip is false, and (trip.paymentType is not set or trip.paymentType is null), we will skip this copy the cash trip to driver
    if (!tx.merchant?.showCashTrip && (typeof trip.paymentType === "undefined" || trip.paymentType === null)) return tx;
    const expiresAt = await this.getExpiresAt(tx);
    await this.pubsubService.publishMessageForCopyTripToDriverProcessing({
      txId: tx.id,
      expiresAt: expiresAt,
    });
    return tx;
  }

  async postAdjustmentProcess(tx: Tx): Promise<Tx> {
    if (!isTxAdjustment(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/postAdjustmentProcess");
    }
    await this.pubsubService.publishMessageForCopyTripToDriverProcessing({
      txId: tx.parentTx?.id,
    });
    return tx;
  }
  private extractPaymentTx(documentData?: FirebaseFirestore.DocumentData): PaymentTxFromDocument[] {
    return (documentData?.payment_information ?? []).map(
      (row: {
        body: string;
        creation_time: Timestamp;
        status: PaymentInformationStatus;
        type: PaymentInformationType;
        transactionId: string;
      }) => {
        if (row.status === PaymentInformationStatus.REQUESTED) {
          return {
            id: JSON.parse(row.body).messageId,
            amount: JSON.parse(row.body).baseAmount,
            status: PaymentInformationStatus.REQUESTED,
            type: row.type,
            gateway: PaymentGatewayTypes.SOEPAY,
            createdAt: row.creation_time.toDate(),
            gatewayResponse: { messageId: JSON.parse(row.body).messageId },
          };
        } else {
          return this.paymentService.extractPaymentTxInfoFromDocument(
            JSON.parse(row.body),
            PaymentGatewayTypes.SOEPAY,
            {
              createdAt: row.creation_time.toDate(),
              type: row.type,
              status: row.status,
              transactionId: row.transactionId,
            },
          );
        }
      },
    );
  }

  async copyMeterTripToUserTrip(tripId: string, dataAfter: FirebaseFirestore.DocumentData) {
    //copy to user/trip if there is user_id
    if (dataAfter.user?.id) {
      await this.appDatabaseService.userTripRepository(dataAfter.user?.id).setCurrentTrip(tripId, dataAfter);
    }
  }

  shouldAddProcessingFlag(tripData: FirebaseFirestore.DocumentData): boolean {
    //if there is processing, we should add processing flag
    const paymentInformationStatusArray = (tripData?.payment_information ?? []).map(
      (row: { status: PaymentInformationStatus }) => row.status,
    );
    const shouldProcessingStatuses = [PaymentInformationStatus.PROCESSING, PaymentInformationStatus.SUCCESS];
    if (
      paymentInformationStatusArray.some((status: PaymentInformationStatus) =>
        shouldProcessingStatuses.includes(status),
      )
    )
      return true;
    return false;
  }
  /**
   * meterTripChangeHandler for meter trip change
   * @param {Change<DocumentSnapshot>}change change from firestore
   * @param {EventContext} context of the change
   */
  async meterTripChange(
    meterId: string,
    tripId: string,
    dataAfter: FirebaseFirestore.DocumentData,
    dataBefore?: FirebaseFirestore.DocumentData,
  ) {
    // Send the trip to the trip processing topic in pubsub
    const paymentTxsAfter: PaymentTxFromDocument[] = this.extractPaymentTx(dataAfter);

    const txData = dataAfter ?? {};
    if (this.shouldAddProcessingFlag(dataAfter)) {
      txData.is_payment_processing = true;
    }
    delete txData.payment_information;

    // Converting the Firestore Timestamps to Date objects
    txData.creation_time = txData.creation_time?.toDate();
    txData.trip_end = txData.trip_end?.toDate();
    txData.last_update_time = txData.last_update_time?.toDate();
    txData.trip_start = txData.trip_start?.toDate();
    if (txData.estimated_dash_amount == null) {
      txData.estimated_dash_amount =
        (txData?.trip_total ?? 0) +
        (txData?.dash_tips ?? 0) +
        ((txData?.trip_total ?? 0) + (txData?.dash_tips ?? 0)) * (txData?.dash_fee_rate ?? 0) +
        (txData?.dash_fee_constant ?? 0);
    }
    const paymentTxsBefore: PaymentTxFromDocument[] = this.extractPaymentTx(dataBefore);
    const newPaymentTxs = paymentTxsAfter.filter(
      (paymentTxAfter) => !paymentTxsBefore.find((paymentTxBefore) => paymentTxBefore.id === paymentTxAfter.id),
    );
    let doVoid = false;
    if (
      newPaymentTxs.length > 0 &&
      newPaymentTxs.find(
        (paymentTx) =>
          paymentTx.status === PaymentInformationStatus.SUCCESS &&
          (paymentTx.type === PaymentInformationType.AUTH || paymentTx.type === PaymentInformationType.SALE),
      )
    ) {
      doVoid = true;
    }
    const TAPXI = "TAPXI";
    let txApp = await this.cacheManager.get<TxApp>(TAPXI);
    if (!txApp) {
      const txAppFromSql = await this.txAppRepository.appByNameOrCreate(TxAppsNames.TAPXI);
      await this.cacheManager.set(TAPXI, txAppFromSql, 0);
      txApp = txAppFromSql;
    }
    const isTripEnd = !dataBefore?.trip_end && Boolean(dataAfter.trip_end);
    const tripEndForUserApp = Boolean(txData.user?.id) && isTripEnd && !dataAfter.hail_id;
    const isCaptureInApp = Boolean(txData.user?.id) && isTripEnd && Boolean(dataAfter.hail_id);

    const messageId = await this.pubsubService.publishMessageForTripProcessing({
      tx: {
        txApp,
        type: TxTypes.TRIP,
        id: tripId,
        data: this.utilsService.case.camelizeKeys(txData),
      },
      ...(txData.driver?.id
        ? {
            merchant: {
              name: txData.driver.name,
              phoneNumber: txData.driver.id,
            },
          }
        : {}),
      paymentTx: paymentTxsAfter,
      txProcessed: !txData.user?.id && isTripEnd,
      doVoid: doVoid,
      isDirectSale: tripEndForUserApp,
      isAfterTxEnd: Boolean(dataBefore?.trip_end) && Boolean(dataAfter.trip_end),
      isCaptureInApp,
      settlementFleetId: txData.settle_to_fleet ? txData.fleet_id : undefined,
      isTripEnd,
    });
    this.logger.info("message published: ", { tx: tripId, messageId: messageId });
    if (Boolean(txData.user?.id) && !dataBefore?.trip_end && Boolean(dataAfter.trip_end)) {
      this.logger.info("check current billing", {
        tx: tripId,
        billing: txData.billing,
        hail_id: txData.hail_id,
        total: txData.total,
      });
      await this.pushNotificationToUserWhenTripEnd(
        tripId,
        txData.hail_id ? txData.billing.total : txData.total,
        txData.user.id,
      );
    }
    if (isCaptureInApp) {
      this.logger.info("check to send first wati message after hailing trip ends", {
        tx: tripId,
        userId: txData.user.id,
        hailId: txData.hail_id,
      });
      await this.checkFirstHailingTripEnd(tripId, txData.user.id, txData.trip_end, txData.language);
    }
  }

  async pushNotificationToUserWhenTripEnd(tripId: string, total: number, appDatabaseId: string) {
    const user = await this.userRepository.findAppUserById(appDatabaseId);
    const userNotificationTokens = await this.userNotificationTokenRepository.find({
      where: { user: { id: user.id } },
    });
    if (!userNotificationTokens || userNotificationTokens.length === 0) return;
    const language = user.preferredLanguage ?? PreferredLanguageType.EN;
    const message = await this.fcmService.getTemplateMessageFromFirestore(
      NotificationTriggerEventType.TRIP_END,
      NotificationRecipientType.RIDER,
      language,
    );
    if (!message) throw errorBuilder.pushNotification.wrongTemplate(NotificationTriggerEventType.TRIP_END);
    const notification: Notification = {
      title: message.title,
      body: message.body.replace("{total}", total.toString()),
    };
    await this.fcmService.pushNotificationToSpecificDevicesWithToken(
      userNotificationTokens.map((token) => token.token),
      notification,
      {
        clickEvent: ClickNotificationEventType.GO_TO_TRANSACTIONS_HISTORY_PAGE_IN_USER_APP,
        tripId: tripId,
      },
    );
  }

  async checkFirstHailingTripEnd(txId: string, userId: string, tripEnd: Date, language: string) {
    const user = await this.userRepository.findAppUserById(userId);
    if (!user) {
      throw errorBuilder.user.notFoundInSql(userId);
    }
    const alreadyExistHailingTripForUser = await this.txRepository
      .createQueryBuilder("tx")
      .where("tx.type = :type", { type: TxTypes.TRIP })
      .andWhere("tx.userId = :userId", { userId: user.id })
      .andWhere("tx.id != :txId", { txId })
      .andWhere("tx.metadata->>'type' = :metadataType", { metadataType: "HAIL" })
      .getOne();
    if (alreadyExistHailingTripForUser) {
      this.logger.info("User already has a hailing trip", { userId });
      return;
    }

    this.logger.info("User does not have a hailing trip before, should send first wati message", { userId });
    return await this.pubsubService.publishMessageForMessageProcessingParams({
      metadata: {
        schemeVersion: "1.0",
        createdAt: tripEnd,
      },
      recipient: {
        phone: user.phoneNumber,
      },
      tranId: txId,
      channel: ChannelTypes.WHATSAPP,
      language: this.utilsService.language.getLanguageType(language),
      params: {},
      messageId: txId,
      template: TemplateTypesText.FIRST_HAIL_TRIP_COMPLETED,
    });
  }

  /**
   * meterTripCreateHandler for meter trip create
   * @param {string} meterId meter id
   * @param {string} tripId trip ip
   * @param {DocumentData} data FirebaseFirestore.DocumentData
   */
  async meterTripCreate(meterId: string, tripId: string, data: FirebaseFirestore.DocumentData) {
    const txData = data ?? {};

    await this.appDatabaseService.runTransaction(async (transaction) => {
      const latestTripDocument = await this.appDatabaseService.meterTripRepository(meterId).findOneById(tripId);
      if (!latestTripDocument) {
        throw errorBuilder.meter.tripNotFound(meterId, tripId);
      }
      //copy to user/trip if there is user_id
      if (latestTripDocument.user?.id) {
        await this.appDatabaseService
          .userTripRepository(latestTripDocument.user?.id)
          .setCurrentTrip(tripId, latestTripDocument);
      }
      let newFieldsToAddToCurrentTrip: Record<string, any> = {};
      if (
        latestTripDocument.session &&
        latestTripDocument.session.id &&
        (!latestTripDocument.type || latestTripDocument.type !== "HAIL")
      ) {
        if (latestTripDocument.locationStart) {
          const addressInChinese = await this.locationService.reverseGeocode({
            language: LocationLanguage.ZH_HK,
            lat: latestTripDocument.locationStart._latitude,
            lng: latestTripDocument.locationStart._longitude,
          });
          const addressName = addressInChinese.formattedAddress;
          newFieldsToAddToCurrentTrip = {
            ...newFieldsToAddToCurrentTrip,
            location_start_address: addressName,
          };
          if (!latestTripDocument.tripItinerary) {
            this.logger.debug("tripItinerary not found, adding to trip from backend function", { tripId });
            newFieldsToAddToCurrentTrip = {
              ...newFieldsToAddToCurrentTrip,
              trip_itinerary: casesUtils.snakeKeys(
                this.initializeTripItinerary(
                  0,
                  latestTripDocument.locationStart._latitude,
                  latestTripDocument.locationStart._longitude,
                  addressInChinese,
                ),
              ),
            };
          }
        } else {
          newFieldsToAddToCurrentTrip = {
            ...newFieldsToAddToCurrentTrip,
            location_start_address: "",
            trip_itinerary: casesUtils.snakeKeys(
              this.initializeTripItinerary(0, 0, 0, { placeId: "", formattedAddress: "", displayName: "" }),
            ),
          };
        }
      }
      if (!latestTripDocument.vehicle || !latestTripDocument.fleetId) {
        const meter = await this.appDatabaseService.meterRepository().findOneById(meterId);
        if (meter && meter.settings) {
          newFieldsToAddToCurrentTrip = {
            ...newFieldsToAddToCurrentTrip,
            settleToFleet: !!meter.settings.settleToFleet,
            ...(meter.settings.fleetId ? { fleetId: meter.settings.fleetId } : {}),
          };
          if (!latestTripDocument.vehicle && meter.settings.vehicleId) {
            const vehicle = await this.appDatabaseService
              .vehicleTypesRepository()
              .findOneById(meter.settings.vehicleId);
            if (vehicle) {
              newFieldsToAddToCurrentTrip = {
                ...newFieldsToAddToCurrentTrip,
                vehicle: {
                  make: vehicle.make,
                  model: vehicle.model,
                  class: vehicle.fleetClass,
                  operatingArea: meter.settings.operatingArea,
                },
              };
            }
          }
        }
      }
      if (!latestTripDocument.billing) {
        newFieldsToAddToCurrentTrip = {
          ...newFieldsToAddToCurrentTrip,
          billing: this.generateBillingObject(casesUtils.snakeKeys(latestTripDocument)),
        };
      }
      newFieldsToAddToCurrentTrip = {
        ...newFieldsToAddToCurrentTrip,
        last_update_time: Timestamp.now(),
      };
      if (Object.keys(newFieldsToAddToCurrentTrip).length > 0) {
        transaction.set(
          this.appDatabaseService.meterTripRepository(meterId).collection.doc(tripId),
          // repo.meterTrip(meterId).doc(tripId),
          {
            ...newFieldsToAddToCurrentTrip,
          },
          { merge: true },
        );
      }
    });
    const sessionId = txData.session?.id;
    if (!sessionId) return;
    const session = await this.appDatabaseService.sessionRepository().getSession(sessionId);
    if (!session?.endTime || !(session.endTime instanceof Date)) return;
    const trip_start_time = txData.trip_start?.toDate();
    if (session.endTime < trip_start_time) {
      await this.appDatabaseService.meterTripRepository(meterId).removeDriverAndSession(tripId);
    }
  }

  /**
   * calculate the trip total amount, if incorrect, will skip capture
   * @param tx Tx
   * @returns { result: boolean; reason: string }
   */
  isTxCalculationCorrect(tx: Tx, isDirectSale: boolean = false): { result: boolean; reason: string } {
    if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/isTxCalculationCorrect");
    }

    const trip: TripDocument = tx.metadata;
    if (trip.paymentType !== PaymentType.DASH) {
      const reason = "Not a DASH trip";
      this.logger.error(reason, { tx: tx.id });
      return { result: false, reason };
    }

    if (trip.tripTotal === undefined || tx.total === undefined) {
      const reason = "Trip has no tripTotal or total";
      this.logger.error(reason, { tx: tx.id });
      return { result: false, reason };
    }

    if (tx.total != undefined && tx.total < 0) {
      const reason = `Tx total (${tx.total}) is negative`;
      this.logger.error(reason, { tx: tx.id });
      return { result: false, reason };
    }

    const paymentTxs = tx.paymentTx ?? [];
    const lastSuccessAuthWithoutCapture = this.paymentService.findLastSuccessAuthWithoutCaptureOrVoid(paymentTxs);
    if (!lastSuccessAuthWithoutCapture || lastSuccessAuthWithoutCapture.amount === undefined) {
      const reason = "No last success auth without capture found";
      this.logger.error(reason, { tx: tx.id });
      return { result: false, reason };
    }

    if (!isDirectSale && lastSuccessAuthWithoutCapture.amount > 0 && tx.total > lastSuccessAuthWithoutCapture.amount) {
      const reason = "Auth amount is smaller than trip total";
      this.logger.error(reason, { tx: tx.id });
      return { result: false, reason };
    }

    const dashFee = tx.dashFee ?? 0;

    if (dashFee <= 0) {
      const reason = `DashFee calculation is not correct. dashFee: ${dashFee}`;
      this.logger.error(reason, { tx: tx.id });
      return { result: false, reason };
    }

    if (!trip.user) {
      //taxipos street hailing only trip, not trip with app user
      const calculatedTotalForTaxiposOnly = roundOneDecimal(
        trip.tripTotal + (trip.dashTips ?? 0) + (trip.dashFee ?? 0) + (trip.discountAmount ?? 0),
      );
      if (tx.total !== calculatedTotalForTaxiposOnly) {
        const reason = `Total calculation is not correct. dashFee: ${dashFee}, calculatedTotalForTaxiposOnly: ${calculatedTotalForTaxiposOnly}, total: ${tx.total}`;
        this.logger.error(reason, { tx: tx.id });
        return { result: false, reason };
      }
    }

    return { result: true, reason: "OK!" };
  }

  /**
   * should update tx metadata
   * @param currentTx Tx
   * @param newTx Tx
   * @returns boolean
   */
  shouldUpdateTxMetadata(currentTx: Tx, newTx: Tx) {
    if (!isTxTrip(currentTx)) {
      throw errorBuilder.transaction.wrongImplement(currentTx.type, "TripService/shouldUpdateTxMetadata");
    }
    const currentTrip: TripDocument = currentTx.metadata;
    if (!isTxTrip(newTx)) {
      throw errorBuilder.transaction.wrongImplement(newTx.type, "TripService/shouldUpdateTxMetadata");
    }
    const newTrip: TripDocument = newTx.metadata;
    const shouldUpdate = newTrip.lastUpdateTime >= currentTrip.lastUpdateTime;
    this.logger.log("shouldUpdateTxMetadata", {
      shouldUpdate,
      newTrip: newTrip.lastUpdateTime,
      currentTrip: currentTrip.lastUpdateTime,
    });
    const condition = `to_timestamp(metadata->>'lastUpdateTime', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"') <= to_timestamp('${new Date(
      newTrip.lastUpdateTime,
    ).toISOString()}', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')`;
    return { shouldUpdate, condition };
  }

  /**
   * should query with all relations
   * @param currentTx Tx
   *
   * @returns boolean
   */
  shouldQueryWithAllRelations(currentTx: Tx): boolean {
    if (!isTxTrip(currentTx)) {
      throw errorBuilder.transaction.wrongImplement(currentTx.type, "TripService/shouldQueryWithAllRelations");
    }
    const trip: TripDocument = currentTx.metadata;
    //if trip is ended, we should query with all relations
    if (trip.tripEnd) {
      return true;
    }
    return false;
  }
  /**
   *
   * get tx ids to payout
   * @param txs Tx[]
   */
  getTxsToPayout(
    txs: Tx[],
    fileContent: PayoutBankFileRow[],
    payouts: Payout[],
  ): { completed: Tx[]; failed: Tx[]; bankProcessing: Tx[] } {
    const merchantsTxs: Record<string, Tx[]> = txs.reduce((acc: Record<string, Tx[]>, tx: Tx) => {
      const merchant = tx.payoutMerchant ?? tx.merchant;
      if (!merchant) {
        return acc;
      }
      if (!acc[merchant.phoneNumber]) {
        acc[merchant.phoneNumber] = [];
      }
      acc[merchant.phoneNumber].push(tx);
      return acc;
    }, {});
    this.logger.info("merchantsTxs", merchantsTxs);
    const txIdAndPayoutFilePair = payouts.flatMap((payout: Payout) =>
      payout.originalRequest.txIds.map((txId: string) => ({
        txId,
        batchFile: payout.batchFile,
      })),
    );
    this.logger.info("txIdAndPayoutFilePair", txIdAndPayoutFilePair);

    return fileContent.reduce(
      (result: { completed: Tx[]; failed: Tx[]; bankProcessing: Tx[] }, fileRow) => {
        if (
          fileRow.status === BankResponseLineItemStatus.COMPLETED ||
          fileRow.status === BankResponseLineItemStatus.CONFIRMED
        ) {
          if (!merchantsTxs[fileRow.merchantId]) {
            return result;
          }
          return merchantsTxs[fileRow.merchantId]?.reduce((resultTx, tx: Tx) => {
            if (
              txIdAndPayoutFilePair.find(
                (pair) => pair.txId === tx.id && pair.batchFile === fileRow.originalBatchFileName,
              )
            ) {
              resultTx.completed.push(tx);
            }
            return resultTx;
          }, result);
        } else if (fileRow.status === BankResponseLineItemStatus.RECEIVED) {
          if (!merchantsTxs[fileRow.merchantId]) {
            return result;
          }
          return merchantsTxs[fileRow.merchantId]?.reduce((resultTx, tx: Tx) => {
            if (
              txIdAndPayoutFilePair.find(
                (pair) => pair.txId === tx.id && pair.batchFile === fileRow.originalBatchFileName,
              )
            ) {
              resultTx.bankProcessing.push(tx);
            }
            return resultTx;
          }, result);
        }
        return merchantsTxs[fileRow.merchantId]?.reduce((resultTx, tx: Tx) => {
          if (
            txIdAndPayoutFilePair.find(
              (pair) => pair.txId === tx.id && pair.batchFile === fileRow.originalBatchFileName,
            )
          ) {
            resultTx.failed.push(tx);
          }
          return resultTx;
        }, result);
      },
      { completed: [], failed: [], bankProcessing: [] },
    );
  }

  /**
   * post payout process
   * @param txs Tx[]
   * @returns Promise<Tx[]>
   */
  async postPayoutProcess(txs: Tx[]): Promise<Tx[]> {
    if (txs.length < 1) return txs;

    const merchantPhonesByPayoutType = txs.reduce<{ merchant: Set<string>; payoutMerchant: Set<string> }>(
      (merchantPhonesByPayoutType, tx) => {
        if (!tx.merchant) {
          return merchantPhonesByPayoutType;
        }
        if (tx.payoutMerchant) {
          merchantPhonesByPayoutType.payoutMerchant.add(tx.merchant.phoneNumber);
        } else {
          merchantPhonesByPayoutType.merchant.add(tx.merchant.phoneNumber);
        }
        return merchantPhonesByPayoutType;
      },
      { merchant: new Set<string>(), payoutMerchant: new Set<string>() },
    );

    const now = new Date();
    await Promise.allSettled(
      Array.from(merchantPhonesByPayoutType.merchant).map((merchantPhoneNumber) => {
        const template = NotificationType.PAYOUT_PAID;
        const notification: NotificationDocument = {
          created_on: now,
          locale: LanguageOption.ZHHK,
          message: notificationTemplate[template].body,
          status: NotificationStatus.NEW,
          title: notificationTemplate[template].title,
          type: NotificationDocumentType.PAYOUT,
        };
        return this.appDatabaseService.driverNotificationsRepository(merchantPhoneNumber).collection.add(notification);
      }),
    );

    await Promise.allSettled(
      Array.from(merchantPhonesByPayoutType.payoutMerchant).map((merchantPhoneNumber) => {
        const template = NotificationType.PAYOUT_PAID_TO_FLEET;
        const notification: NotificationDocument = {
          created_on: now,
          locale: LanguageOption.ZHHK,
          message: notificationTemplate[template].body,
          status: NotificationStatus.NEW,
          title: notificationTemplate[template].title,
          type: NotificationDocumentType.PAYOUT,
        };
        return this.appDatabaseService.driverNotificationsRepository(merchantPhoneNumber).collection.add(notification);
      }),
    );

    return txs;
  }

  /**
   * Generate Receipt based on tx
   * @param tx Tx
   * @returns txReceipt
   */
  async getReceiptByTx(tx: Tx, language: ReceiptLanguageType): Promise<TxReceipt> {
    if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/getReceiptByTx");
    }
    const txReceiptTrip = new TxReceiptTrip();
    const metadata: TripDocument = tx.metadata;

    txReceiptTrip.id = tx.id;
    txReceiptTrip.language = metadata.language ?? "zh-hk";
    txReceiptTrip.licensePlate = metadata.licensePlate;
    // start
    txReceiptTrip.tripStart = metadata.tripStart;
    txReceiptTrip.locationStart = metadata.locationStart;
    txReceiptTrip.locationStartAddress = metadata.locationStartAddress;
    //end
    txReceiptTrip.tripEnd = metadata.tripEnd;
    txReceiptTrip.locationEnd = metadata.locationEnd;
    txReceiptTrip.locationEndAddress = metadata.locationEndAddress;
    // $ & distance
    txReceiptTrip.fare = metadata.fare;
    txReceiptTrip.extra = metadata.extra;
    txReceiptTrip.dashFee = tx.dashFee ?? 0;
    txReceiptTrip.total = tx.total ?? 0;
    txReceiptTrip.dashTips = metadata.dashTips ?? 0;
    txReceiptTrip.tripTotal = metadata.tripTotal;
    txReceiptTrip.additionalBookingFee = metadata.billing?.additionalBookingFee ?? 0;
    txReceiptTrip.boostAmount = metadata.billing?.boostAmount ?? 0;
    txReceiptTrip.distance = metadata.distance;
    txReceiptTrip.adjustment = tx.adjustment ?? 0;
    txReceiptTrip.isDashMeter = metadata.isDashMeter;
    txReceiptTrip.isHailing = metadata.type === TripType.HAIL;
    txReceiptTrip.billing = metadata.billing;
    txReceiptTrip.vehicle = metadata.vehicle;
    //discount
    if (tx.discount && tx.discount) {
      const discount = await this.discountRepository.findOne({
        where: { tx: { id: tx.id } },
        relations: ["campaign"],
      });
      if (discount && discount.state === DiscountState.REDEEMED) {
        txReceiptTrip.discountAmount = tx.discount;
        txReceiptTrip.campaignName =
          language === ReceiptLanguageType.ENHK ? discount.campaign.nameEn : discount.campaign.nameTc;
      }
    }
    //payment
    txReceiptTrip.paymentType = metadata.paymentType;

    if (metadata.type === TripType.HAIL) {
      // start
      const start = metadata.tripItinerary?.[0];
      const startLocation = new Location();
      startLocation._latitude = start?.lat ?? 0;
      startLocation._longitude = start?.lng ?? 0;
      txReceiptTrip.locationStart = startLocation;
      txReceiptTrip.locationStartAddress = start?.i18n?.zhHK?.displayName ?? "";
      if (language === ReceiptLanguageType.ENHK && start?.i18n?.en?.displayName) {
        txReceiptTrip.locationStartAddress = start?.i18n?.en?.displayName;
      }

      //end
      const end = metadata.tripItinerary?.[1];
      const endLocation = new Location();
      endLocation._latitude = end?.lat ?? 0;
      endLocation._longitude = end?.lng ?? 0;
      txReceiptTrip.locationEnd = endLocation;
      txReceiptTrip.locationEndAddress = end?.i18n?.zhHK?.displayName ?? "";
      if (language === ReceiptLanguageType.ENHK && end?.i18n?.en?.displayName) {
        txReceiptTrip.locationEndAddress = end?.i18n?.en?.displayName;
      }

      txReceiptTrip.fare = metadata.billing?.fare ?? 0;
      txReceiptTrip.extra = metadata.billing?.extra ?? 0;
      txReceiptTrip.dashFee = metadata.billing?.dashFeeTotal ?? 0;
      txReceiptTrip.total = metadata.billing?.total ?? 0;
      txReceiptTrip.discountAmount = metadata.billing?.discount ?? 0;
      txReceiptTrip.dashTips = metadata.billing?.dashTips ?? 0;
      txReceiptTrip.boostAmount = metadata.billing?.boostAmount ?? 0;
    }
    const lastCapturedPaymentTx = TripService.getLastPaymentTxByStatusAndType(
      tx,
      [PaymentInformationType.CAPTURE, PaymentInformationType.SALE],
      PaymentInformationStatus.SUCCESS,
    );
    if (lastCapturedPaymentTx === undefined) {
      return txReceiptTrip;
    }

    const paymentCardInformation = new PaymentCardInformation();
    paymentCardInformation.cardType = lastCapturedPaymentTx?.paymentMethod;
    paymentCardInformation.maskedPan = lastCapturedPaymentTx?.cardNumber;
    txReceiptTrip.paymentCardInformation = paymentCardInformation;

    return txReceiptTrip;
  }

  /**
   * Traverse paymentTx and find the last payment tx matching the status and type
   * @param tx Tx
   * @param types PaymentTxType[] - types
   * @param status PaymentTxStatus
   */
  static getLastPaymentTxByStatusAndType(
    tx: Tx,
    types: PaymentInformationType[],
    status: PaymentInformationStatus,
  ): PaymentTx | undefined {
    return tx.paymentTx
      ?.reverse()
      .filter(
        (paymentTx) => paymentTx.status === status && paymentTx.type != undefined && types.includes(paymentTx.type),
      )[0];
  }

  /**
   * update metadata of a tx
   * @param tx Tx
   * @param data Record<string, any>
   * @returns Promise<Tx>
   */
  async updateMetadata(tx: Tx, data: Record<string, any>, isHailingSetTips: boolean = false): Promise<Tx> {
    if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/updateMetadata");
    }
    const currentTrip: TripDocument = tx.metadata;
    const updatedTrip = await this.appDatabaseService
      .meterTripRepository(currentTrip.licensePlate)
      .updateTrip(tx.id, data);

    const driverId = tx.merchant?.phoneNumber;
    if (driverId) {
      const foundDriverTrip = await this.appDatabaseService.driverTripRepository(driverId).findOneById(tx.id);
      if (foundDriverTrip) {
        await this.appDatabaseService.driverTripRepository(driverId).updateTripWithObject(tx.id, data);
        const sessionId = currentTrip.session?.id;
        if (sessionId) {
          await this.appDatabaseService
            .driverSessionTripRepository(driverId, sessionId)
            .updateSessionTripWithObject(tx.id, data);
        }
      }
    }
    if (updatedTrip) {
      delete (updatedTrip as any).should_update_last_update_time;
      delete (updatedTrip as any).shouldUpdateLastUpdateTime;
      if (updatedTrip.paymentInformation) {
        delete updatedTrip.paymentInformation;
      }
      tx.metadata = updatedTrip;
      if (!isHailingSetTips) {
        const { dashFee, payoutAmount } = this.getTxMonetary(tx);
        tx.dashFee = dashFee;
        tx.payoutAmount = payoutAmount;
      } else {
        const trip: TripDocument = updatedTrip;
        const newBilling = await this.updateBillingForTips(casesUtils.snakeKeys(trip));
        const updatedTripWithBilling = await this.appDatabaseService
          .meterTripRepository(currentTrip.licensePlate)
          .updateTrip(tx.id, {
            billing: newBilling,
            lastUpdateTime: Timestamp.now(),
          });
        tx.metadata = updatedTripWithBilling;
      }
      return this.txRepository.save(tx);
    } else {
      return tx;
    }
  }

  /**
   * add to user/trip from meter/trip
   * @param tx Tx
   * @param appDatabaseId string
   * @returns Promise<Tx>
   */
  async addToUserCurrentTx(tx: Tx, appDatabaseId: string): Promise<Tx> {
    if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/addToUserCurrentTx");
    }
    const trip: TripDocument = tx.metadata;
    if (!trip.user) {
      throw errorBuilder.transaction.trip.tripNotPairedWithUser(tx.id, appDatabaseId);
    }
    await this.appDatabaseService.userTripRepository(trip.user.id).setCurrentTrip(tx.id, trip);
    return tx;
  }

  async unlock(tx: Tx, userId: string): Promise<Tx> {
    if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/unlock");
    }

    const meterId = tx.metadata.licensePlate;

    if (!meterId) {
      throw errorBuilder.transaction.trip.missingLicencePlate(tx.id);
    }

    const activeLock = await this.appDatabaseService.meterTripLockRepository(meterId, tx.id).getActiveLock();

    if (!activeLock) {
      throw errorBuilder.transaction.alreadyUnlocked(tx.id);
    }

    if (activeLock.createdBy !== userId) {
      throw errorBuilder.transaction.lockedByOtherUser(tx.id);
    }

    activeLock.unlockedAt = new Date();

    await this.appDatabaseService.meterTripLockRepository(meterId, tx.id).set(activeLock);

    return tx;
  }

  async lock(tx: Tx, userId: string, timeout: number): Promise<LockDocument> {
    if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/lock");
    }

    const meterId = tx.metadata.licensePlate;

    if (!meterId) {
      throw errorBuilder.transaction.trip.missingLicencePlate(tx.id);
    }

    const activeLock = await this.appDatabaseService.meterTripLockRepository(meterId, tx.id).getActiveLock();

    if (activeLock && activeLock.createdBy != userId) {
      throw errorBuilder.transaction.alreadyLocked(tx.id);
    }

    return this.appDatabaseService.meterTripLockRepository(meterId, tx.id).createLock(userId, timeout);
  }

  getDiscountId(tx: Tx): string | undefined {
    if (!isTxTrip(tx)) {
      throw errorBuilder.incentive.discount.invalidTxType(tx.id, TxTypes.TRIP);
    }
    const trip = tx.metadata;
    if (trip.tripEnd) {
      throw errorBuilder.incentive.discount.tripAlreadyEnded(tx.id);
    }
    return trip.discountId;
  }

  async setTipFromUserApp(tx: Tx, userId: string, tip: number): Promise<Tx> {
    if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/setTipFromUserApp");
    }
    const trip = tx.metadata;
    if (trip.tripEnd) {
      throw errorBuilder.transaction.trip.tripAlreadyEnded(tx.id);
    }
    if (!trip.user || trip.user.id !== userId) {
      throw errorBuilder.transaction.trip.tripNotPairedWithUser(tx.id, userId);
    }
    let isHailingSetTips = false;
    if (trip.type == TripType.HAIL) {
      isHailingSetTips = true;
    }
    await this.updateMetadata(tx, { dashTips: tip }, isHailingSetTips);
    return tx;
  }

  async setRating(tx: Tx, userId: string, rating: Rating): Promise<Tx> {
    if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/setRating");
    }
    const trip = tx.metadata;
    if (!trip.user || trip.user.id !== userId) {
      throw errorBuilder.transaction.trip.tripNotPairedWithUser(tx.id, userId);
    }
    await this.updateMetadata(tx, { rating: rating, rateTime: new Date() });
    return tx;
  }

  getTxDate(tx: Tx): Date {
    if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/getTxDate");
    }
    const trip = tx.metadata;
    return trip.tripEnd;
  }

  isTxAbleToPair(tx: Tx): boolean {
    if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/isTxAbleToPair");
    }
    const trip = tx.metadata;
    if (trip.tripEnd) {
      throw errorBuilder.transaction.trip.tripAlreadyEnded(tx.id);
    }
    if (trip.user) {
      throw errorBuilder.qrcode.alreadyPaired(trip.user.id);
    }
    if (!trip.user && trip.paymentType === PaymentType.DASH) {
      throw errorBuilder.qrcode.alreadyPaired("taxipos_user");
    }
    return true;
  }

  async addExtraMetadata(tx: Tx, language?: LocalizedLanguage): Promise<Record<string, any>> {
    if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/addExtraMetadata");
    }
    const trip = tx.metadata;
    const trip_itinerary: Record<string, any>[] | undefined = trip.tripItinerary;
    if (
      trip_itinerary &&
      trip_itinerary.length == 1 &&
      trip_itinerary[0].index == 0 &&
      (!trip.type || trip.type !== "HAIL")
    ) {
      const addressInEnglish = await this.locationService.reverseGeocode({
        language: language === LocalizedLanguage.EN ? LocationLanguage.EN : LocationLanguage.ZH_HK,
        lat: trip.locationStart._latitude,
        lng: trip.locationStart._longitude,
      });
      trip_itinerary[0].i18n.en = {
        display_name: addressInEnglish.displayName,
        formatted_address: addressInEnglish.formattedAddress,
      };
      return { trip_itinerary: trip_itinerary };
    }
    return {};
  }

  async convertLocation(
    meterId: string,
    tripId: string,
    fieldName: string,
    lat: number,
    lng: number,
    index: number,
    currentItinerary?: Record<string, any>[],
    appUserLanguage?: string,
  ): Promise<{ address: string; trip_itinerary?: Record<string, any>[] }> {
    const addressInChinese = await this.locationService.reverseGeocode({
      language: LocationLanguage.ZH_HK,
      lat: lat,
      lng: lng,
    });
    const addressName = addressInChinese.formattedAddress;
    const newFieldName = fieldName + "_address";
    let trip_itinerary: Record<string, any>[] | undefined = currentItinerary;
    if (index === 0 && !currentItinerary) {
      trip_itinerary = this.initializeTripItinerary(index, lat, lng, addressInChinese);
    }
    if (index === 1 && currentItinerary && currentItinerary.length === 1) {
      trip_itinerary = currentItinerary;
      let addressInEnglish: LocationReverseGeocodeResponse | undefined = undefined;
      if (appUserLanguage && appUserLanguage === LocalizedLanguage.EN) {
        addressInEnglish = await this.locationService.reverseGeocode({
          language: LocationLanguage.EN,
          lat: lat,
          lng: lng,
        });
      }
      const tripItineraryForEnd = this.generateTripItineraryForEnd(lat, lng, addressInChinese, addressInEnglish);
      trip_itinerary.push(tripItineraryForEnd);
    }
    this.logger.debug("convertLocation", { tripId, newFieldName, addressName, trip_itinerary });
    return { address: addressName, trip_itinerary: trip_itinerary };
  }

  initializeTripItinerary(
    index: number,
    lat: number,
    lng: number,
    addressResult: LocationReverseGeocodeResponse,
  ): Record<string, any>[] {
    return [
      {
        i18n: {
          [LocalizedLanguage.ZHHK]: {
            display_name: addressResult.displayName,
            formatted_address: addressResult.formattedAddress,
          },
        },
        index,
        lat,
        lng,
        place_id: addressResult.placeId,
      },
    ];
  }

  generateTripItineraryForEnd(
    lat: number,
    lng: number,
    addressInChinese: LocationReverseGeocodeResponse,
    addressInEnglish?: LocationReverseGeocodeResponse,
  ): Record<string, any> {
    return addressInEnglish
      ? {
          i18n: {
            [LocalizedLanguage.EN]: {
              display_name: addressInEnglish.displayName,
              formatted_address: addressInEnglish.formattedAddress,
            },
            [LocalizedLanguage.ZHHK]: {
              display_name: addressInChinese.displayName,
              formatted_address: addressInChinese.formattedAddress,
            },
          },
          index: 1,
          lat: lat,
          lng: lng,
          place_id: addressInChinese.placeId,
        }
      : {
          i18n: {
            [LocalizedLanguage.ZHHK]: {
              display_name: addressInChinese.displayName,
              formatted_address: addressInChinese.formattedAddress,
            },
          },
          index: 1,
          lat: lat,
          lng: lng,
          place_id: addressInChinese.placeId,
        };
  }

  async updateBilling(meterId: string, tripId: string, dataAfter: FirebaseFirestore.DocumentData) {
    await this.appDatabaseService.meterTripRepository(meterId).updateTrip(tripId, {
      billing: this.generateBillingObject(dataAfter),
      lastUpdateTime: Timestamp.now(),
    });
  }

  async updateTripWhenTripEnd(meterId: string, tripId: string, data: Record<string, any>) {
    await this.appDatabaseService.meterTripRepository(meterId).updateTrip(tripId, data);
  }

  generateBillingObject(data: FirebaseFirestore.DocumentData): Record<string, any> {
    let currentBilling: Record<string, any> = data.billing ?? {};
    const fieldsCanBeUpdatedDirectly = ["fare", "extra", "dash_tips", "trip_total", "boost_amount"];
    forEach(fieldsCanBeUpdatedDirectly, (field) => {
      if (data[field] !== undefined) {
        currentBilling = { ...currentBilling, [field]: data[field] };
      }
    });
    const dashFeeConstant = data.dash_fee_constant ?? currentBilling.dash_fee_settings?.dash_fee_constant ?? 0;
    const dashFeeRate = data.dash_fee_rate ?? currentBilling.dash_fee_settings?.dash_fee_rate ?? 0;
    const dash_transaction_fee = roundUpOneDecimal(
      dashFeeConstant +
        dashFeeRate *
          ((data.trip_end
            ? data.trip_total ?? 0
            : data.hail_id
            ? currentBilling.estimated_fare ?? 0
            : data.trip_total ?? 0) +
            (currentBilling.additional_booking_fee ?? 0) +
            (currentBilling.dash_booking_fee ?? 0) +
            (currentBilling.fleet_booking_fee ?? 0) +
            (data.dash_tips ?? 0) +
            (currentBilling.boost_amount ?? 0)),
    );
    const dash_fee_total = roundUpOneDecimal(dash_transaction_fee + (currentBilling.dash_booking_fee ?? 0));

    // TODO: Should use the actual trip total and discount calculation to set this rather than calculating separately
    const totalBeforeDiscount = roundOneDecimal(
      (data.trip_end
        ? data.trip_total ?? 0
        : data.hail_id
        ? currentBilling.estimated_fare ?? 0
        : data.trip_total ?? 0) +
        (currentBilling.additional_booking_fee ?? 0) +
        (currentBilling.dash_booking_fee ?? 0) +
        (currentBilling.fleet_booking_fee ?? 0) +
        (data.dash_tips ?? 0) +
        (currentBilling.boost_amount ?? 0) +
        dash_transaction_fee,
    );
    let totalAvailableDiscount = -((totalBeforeDiscount ?? 0) - (data.dash_tips ?? 0));
    let discountTotal = 0;
    let discountThirdParty = 0;
    let discountDash = 0;
    if (data.discount_rules || currentBilling.discount_settings?.discount_rules_third_party) {
      const discount_rules = data.discount_rules ?? currentBilling.discount_settings?.discount_rules_third_party;
      try {
        discountThirdParty = roundOneDecimal(
          Math.max(
            this.jsonLogicService.apply(JSON.parse(discount_rules), {
              dash_transaction_fee,
              dash_booking_fee: currentBilling.dash_booking_fee ?? 0,
            }),
            totalAvailableDiscount,
          ),
        );
        discountTotal += discountThirdParty;
        totalAvailableDiscount -= discountThirdParty;
      } catch (e) {
        this.logger.error("Error in applying third party discount rules", { error: e });
      }
    }
    if (data.discount_rules_dash || currentBilling.discount_settings?.discount_rules_dash) {
      const discount_rules = data.discount_rules_dash ?? currentBilling.discount_settings?.discount_rules_dash;
      try {
        discountDash = roundOneDecimal(
          Math.max(
            this.jsonLogicService.apply(JSON.parse(discount_rules), {
              dash_transaction_fee,
              dash_booking_fee: currentBilling.dash_booking_fee ?? 0,
            }),
            totalAvailableDiscount,
          ),
        );
        discountTotal += discountDash;
        totalAvailableDiscount -= discountDash;
      } catch (e) {
        this.logger.error("Error in applying dash discount rules", { error: e });
      }
    }

    const total = roundOneDecimal(
      (data.trip_end
        ? data.trip_total ?? 0
        : data.hail_id
        ? currentBilling.estimated_fare ?? 0
        : data.trip_total ?? 0) +
        (currentBilling.additional_booking_fee ?? 0) +
        (currentBilling.dash_booking_fee ?? 0) +
        (currentBilling.fleet_booking_fee ?? 0) +
        (data.dash_tips ?? 0) +
        (currentBilling.boost_amount ?? 0) +
        dash_transaction_fee +
        discountTotal,
    );
    currentBilling = {
      ...currentBilling,
      dash_transaction_fee,
      dash_fee_total,
      discount: discountTotal,
      discount_third_party: discountThirdParty,
      discount_dash: discountDash,
      total,
    };
    if (!currentBilling.dash_fee_settings) {
      currentBilling = {
        ...currentBilling,
        dash_fee_settings: {
          dash_fee_constant: dashFeeConstant,
          dash_fee_rate: dashFeeRate,
        },
      };
    }

    let discountRules: Record<string, any> = {};
    if (data.discount_id && data.discount_rules) {
      discountRules = {
        discount_rules_third_party: data.discount_rules,
      };
    }
    if (data.discount_id_dash && data.discount_rules_dash) {
      discountRules = {
        ...discountRules,
        discount_rules_dash: data.discount_rules_dash,
      };
    }

    if ((data.discount_id && data.discount_rules) || (data.discount_id_dash && data.discount_rules_dash)) {
      currentBilling = {
        ...currentBilling,
        discount_settings: discountRules,
      };
    }
    return currentBilling;
  }

  /**
   * Update billing for tips and total only - workaround used for hailing trips only
   */
  async updateBillingForTips(data: FirebaseFirestore.DocumentData): Promise<Record<string, any>> {
    let currentBilling: Record<string, any> = data.billing ?? {};

    const previousTips = currentBilling.dash_tips ?? 0;
    const previousTotal = currentBilling.total ?? 0;
    if (data["dash_tips"] !== undefined) {
      currentBilling = { ...currentBilling, dash_tips: data["dash_tips"] };
    }

    const newTotal = previousTotal - previousTips + (data["dash_tips"] ?? 0);
    if (newTotal !== undefined) {
      currentBilling = { ...currentBilling, total: newTotal };
    }

    return currentBilling;
  }

  async updateHailingTripWithDiscounts(tx: Tx, hailingTx?: Tx, meterId?: string): Promise<void> {
    if (!isTxTrip(tx)) {
      this.logger.error("[updateHailingTripWithDiscounts] Invalid tx type", { tx });
      return;
    }

    if (hailingTx) {
      if (!isTxHailing(hailingTx)) {
        this.logger.error("[updateHailingTripWithDiscounts] Invalid hailing tx type", { hailingTx });
        return;
      }

      const hailingRequest: TxHailingMetadata = hailingTx.metadata;
      if (hailingRequest && hailingRequest.userId && hailingRequest.id && hailingRequest.paymentDetails) {
        let thirdPartyDiscountUpdateSuccess = false;
        if (hailingRequest.discounts.discountIdThirdParty) {
          thirdPartyDiscountUpdateSuccess = await this.campaignService.applyDiscountToTx(
            hailingRequest.discounts.discountIdThirdParty,
            tx,
          );
        }

        let dashDiscountUpdateSuccess = false;
        if (hailingRequest.discounts.discountIdDash) {
          dashDiscountUpdateSuccess = await this.campaignService.applyDiscountToTx(
            hailingRequest.discounts.discountIdDash,
            tx,
          );
        }

        if (meterId && (thirdPartyDiscountUpdateSuccess || dashDiscountUpdateSuccess)) {
          this.logger.debug("[updateHailingTripWithDiscounts] Updating meter trip with discounts", {
            meterId,
            thirdPartyDiscountUpdateSuccess,
            dashDiscountUpdateSuccess,
          });

          let updatedTrip: DeepPartial<TripDocument> = {
            lastUpdateTime: Timestamp.now(),
          };

          if (thirdPartyDiscountUpdateSuccess) {
            updatedTrip = {
              ...updatedTrip,
              discountId: hailingRequest.discounts.discountIdThirdParty,
              discountRules: hailingRequest.discounts.discountRulesThirdParty,
            };
          }
          if (dashDiscountUpdateSuccess) {
            updatedTrip = {
              ...updatedTrip,
              discountIdDash: hailingRequest.discounts.discountIdDash,
              discountRulesDash: hailingRequest.discounts.discountRulesDash,
            };
          }
          await this.appDatabaseService.meterTripRepository(meterId).updateTrip(hailingRequest.id, updatedTrip);
        }
      }
    }
  }

  async calculateDiscountsAndUpdateTx(
    tx: Tx,
    discounts: TxDiscounts,
  ): Promise<{ updatedTx: Tx; isDiscountMatch: boolean; discountAmountFromFirestore?: number }> {
    if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/calculateDiscountsAndUpdateTx");
    }
    const trip = tx.metadata;
    const dashTransactionFee = calculateDashTransactionFee({
      tripTotal: trip.tripTotal,
      dashTips: trip.dashTips,
      dashFeeRate: trip.dashFeeRate,
      dashFeeConstant: trip.dashFeeConstant,
      additionalBookingFee: trip.billing?.additionalBookingFee,
      dashBookingFee: trip.billing?.dashBookingFee,
      fleetBookingFee: trip.billing?.fleetBookingFee,
      boostAmount: trip.billing?.boostAmount,
    });

    const ruleParams = {
      trip_total: trip.tripTotal,
      dash_transaction_fee: dashTransactionFee ?? 0,
      dash_booking_fee: trip.billing?.dashBookingFee ?? 0,
    };

    let totalDiscountAmount = 0;
    let totalAvailableDiscount = -((tx.total ?? 0) - (trip.dashTips ?? 0));
    if (discounts.thirdParty) {
      try {
        const discountAmount = await this.calculateDiscountAmount(
          discounts.thirdParty,
          ruleParams,
          totalAvailableDiscount,
        );
        if (discountAmount < 0) {
          totalDiscountAmount += discountAmount;
          totalAvailableDiscount -= discountAmount;
          tx.discountThirdParty = roundOneDecimal(discountAmount);
          await this.campaignService.setDiscountRedeemedValue(discounts.thirdParty, -discountAmount);
        } else {
          this.logger.debug("[calculateDiscountsAndUpdateTx] Third party discount amount is not valid", {
            discount: tx.discountDash,
            discountAmount,
          });
          await this.campaignService.resetOrFailDiscount(discounts.thirdParty);
        }
      } catch (e) {
        this.logger.error("[calculateDiscountsAndUpdateTx] Error in calculating third party discount", { error: e });
      }
    }
    if (discounts.dash) {
      try {
        const discountAmount = await this.calculateDiscountAmount(discounts.dash, ruleParams, totalAvailableDiscount);
        if (discountAmount < 0) {
          totalDiscountAmount += discountAmount;
          totalAvailableDiscount -= discountAmount;
          tx.discountDash = roundOneDecimal(discountAmount);
          await this.campaignService.setDiscountRedeemedValue(discounts.dash, -discountAmount);
        } else {
          this.logger.debug("[calculateDiscountsAndUpdateTx] Dash discount amount is not valid", {
            discount: tx.discountDash,
            discountAmount,
          });
          await this.campaignService.resetOrFailDiscount(discounts.dash);
        }
      } catch (e) {
        this.logger.error("[calculateDiscountsAndUpdateTx] Error in calculating dash discount", { error: e });
      }
    }

    tx.discount = roundOneDecimal(totalDiscountAmount);
    tx.total = roundOneDecimal((tx.total ?? 0) + totalDiscountAmount);

    const updatedTx = await this.txRepository.save(tx);

    this.logger.debug("[calculateDiscountsAndUpdateTx] Calculated discounts", {
      totalDiscountAmount,
      totalAvailableDiscount,
      trip,
      updatedTx,
    });

    if (
      !trip.user &&
      trip.discountAmount &&
      trip.paymentMethodSelected &&
      isPaymentMethodSelected(trip.paymentMethodSelected)
    ) {
      return {
        updatedTx,
        isDiscountMatch: totalDiscountAmount === trip.discountAmount,
        discountAmountFromFirestore: trip.discountAmount,
      };
    } else if (trip.user && trip.paymentInstrument) {
      return { updatedTx: updatedTx, isDiscountMatch: true };
    }

    return { updatedTx: tx, isDiscountMatch: true };
  }

  async calculateDiscountAmount(
    discount: Discount,
    ruleParams: Record<string, any>,
    totalAvailableDiscount: number,
  ): Promise<number> {
    const rule = discount.campaign.discountRules;
    if (!rule) {
      throw errorBuilder.incentive.discount.notFoundDiscountRulesForCampaign(discount.campaign.id);
    }

    // Calculate the discount amount, capping at the total available discount
    // Since discount is negative, we take the max of the two values
    const discountAmount = Math.max(this.jsonLogicService.apply(JSON.parse(rule), ruleParams), totalAvailableDiscount);

    return discountAmount;
  }

  async createDiscountsWhenTripEnd(tripId: string, dataAfter: FirebaseFirestore.DocumentData): Promise<TxDiscounts> {
    const txData = dataAfter ?? {};

    const rule = new ApplicationRuleParams();
    rule.transactionType = TxTypes.TRIP;

    // street trip with taxipos
    if (!txData.user && txData.discount_amount && txData.payment_method_selected) {
      rule.transactionSubtype = SubTxTypes.STREET;
      rule.paymentChannel = PaymentChannelType.TAXIPOS;
      rule.paymentInstrumentType = PaymentInstrumentType[txData.payment_method_selected as PaymentInstrumentType];
      const [thirdPartyCampaign, dashCampaign] = await this.campaignService.getApplicableCampaigns(rule);
      if (thirdPartyCampaign || dashCampaign) {
        const tx = await this.txRepository.findOne({ where: { id: tripId } });
        if (!tx) {
          throw errorBuilder.transaction.notFound(tripId);
        }
        const thirdPartyDiscount = thirdPartyCampaign
          ? await this.campaignService.applyCampaign(thirdPartyCampaign, tx)
          : undefined;
        const dashDiscount = dashCampaign ? await this.campaignService.applyCampaign(dashCampaign, tx) : undefined;
        return { thirdParty: thirdPartyDiscount, dash: dashDiscount };
      }
    }

    // street trip with app
    if (txData.user && !txData.hail_id && txData.payment_instrument) {
      const user = await this.userRepository.findAppUserById(txData.user.id);
      rule.userId = user.id;
      rule.transactionSubtype = SubTxTypes.STREET;
      rule.paymentChannel = PaymentChannelType.APP;
      rule.paymentInstrumentType = PaymentInstrumentType[txData.payment_instrument.card_type as PaymentInstrumentType];
      const [thirdPartyCampaign, dashCampaign] = await this.campaignService.getApplicableCampaigns(rule);
      if (thirdPartyCampaign || dashCampaign) {
        const tx = await this.txRepository.findOne({ where: { id: tripId } });
        if (!tx) {
          throw errorBuilder.transaction.notFound(tripId);
        }
        const thirdPartyDiscount = thirdPartyCampaign
          ? await this.campaignService.applyCampaign(thirdPartyCampaign, tx, user)
          : undefined;
        const dashDiscount = dashCampaign
          ? await this.campaignService.applyCampaign(dashCampaign, tx, user)
          : undefined;
        return { thirdParty: thirdPartyDiscount, dash: dashDiscount };
      }
    }

    return { thirdParty: undefined, dash: undefined };
  }

  resetDiscountsForHailingRequest(hailingTx: Tx): Promise<void> {
    throw errorBuilder.factory.notImplemented("TripService/resetDiscountsForHailingRequest");
  }
}
