import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";
import { Relation } from "typeorm";

import { TripDocument } from "../../appDatabase/documents/trip.document";
import PaymentTx from "../../database/entities/paymentTx.entity";
import { HailingApiCreateOrderResponse } from "../../hailing/dto/hailing.api.dto";
import { HailingItineraryStepResponse } from "../../hailing/dto/hailing.dto";
import { HailingCreateOrderBody, PlatformType } from "../../me/modules/meHailing/dto/meHailing.dto";
import { ProcessPaymentInstrumentVerifyBody } from "../../me/modules/mePaymentInstrument/mePaymentInstrument.dto";
import { PaymentGatewayTypes } from "../../payment/dto/paymentGatewayTypes.dto";
import {
  CreatePaymentResponse,
  Initiate3DSecureResponse,
} from "../../payment/modules/paymentInstrument/dto/paymentInstrument.dto";

export class BillingInfoDto {
  @ApiProperty({ required: false, description: "Discount for adjustment" })
  discount?: number;

  @ApiProperty({ required: false, description: "Dash fee rate for adjustment" })
  dashFeeSettings?: any;
}
export class TxAdjustmentMetadata {
  @ApiProperty()
  reason: string;

  @ApiProperty({ required: false, description: "Dash fee rate for adjustment" })
  dashFeeRate?: number;

  @ApiProperty({ required: false, description: "Dash fee constant for adjustment" })
  dashFeeConstant?: number;

  @ApiProperty({
    required: false,
    description: "Billing information for adjustment",
    type: () => BillingInfoDto,
  })
  billing?: BillingInfoDto;
}

export class TxPaymentInstrumentMetadata {
  @ApiProperty()
  instrumentIdentifier: string;

  @ApiProperty()
  paymentGateway: PaymentGatewayTypes;

  @ApiProperty()
  userAppDatabaseId: string;

  @ApiProperty()
  init: Initiate3DSecureResponse;

  @ApiProperty()
  process: CreatePaymentResponse;

  @ApiProperty()
  deviceConfig: ProcessPaymentInstrumentVerifyBody;

  @ApiProperty()
  sessionId: string;

  @ApiProperty()
  isPayerAuthEnroled: boolean;

  @ApiProperty()
  validateAuthenticationResults: Relation<PaymentTx>;

  @ApiProperty()
  instrumentIdentifierResponse: any;

  @ApiProperty()
  paymentInstrumentResponse: any;

  @ApiProperty()
  checkPayerAuthEnrollment: any;

  @ApiProperty()
  token: string;
}

export class TxHailingMetadata extends HailingApiCreateOrderResponse {
  type: string;
  platformType: PlatformType;
  request: HailingCreateOrderBody;
  itinerary: HailingItineraryStepResponse[];
  charges: {
    cancellationFee?: number;
    cancellationFeeBreakdown?: {
      total: number;
      driverPayout: number;
      dashFee: number;
    };
  };
  licensePlate?: string;
  discounts: {
    discountIdThirdParty?: string;
    discountRulesThirdParty?: string;
    discountIdDash?: string;
    discountRulesDash?: string;
  };
  authedAmount?: number;
  session?: {
    id: string;
  };
  completedAt?: Date;
}

/**
 * Tx metadata model that aggregate all tx metadata
 */
export type TxMetadata = TripDocument | TxAdjustmentMetadata | TxPaymentInstrumentMetadata | TxHailingMetadata;

export const txMetadataSchema = Joi.object<TxMetadata>();
