import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn, Relation } from "typeorm";

import { DefaultEntity } from "./defaultEntity";
import Tx from "./tx.entity";
import { AddEventBody } from "../../me/modules/meTransaction/dto/addEvent.dto";
import { TxEventContent, TxEventType } from "../../transaction/dto/txEventType.dto";

@Entity()
export default class TxEvent extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @ManyToOne(() => Tx, (tx) => tx.id)
  tx: Relation<Tx>;

  @Column()
  type: TxEventType;

  @Column({ nullable: true })
  createdBy?: string;

  @Column("jsonb", { nullable: true })
  content?: TxEventContent;

  static fromAddEvent(tx: Tx, createdBy: string, addEventBody: AddEventBody) {
    const txEvent = new TxEvent();
    txEvent.tx = new Tx();
    txEvent.tx.id = tx.id;
    txEvent.type = addEventBody.type;
    txEvent.createdBy = createdBy;
    if (addEventBody.type === TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER) {
      txEvent.content = addEventBody.content;
    } else if (addEventBody.type === TxEventType.HAILING_MERCHANT_PICK_UP_CONFIRMED) {
      txEvent.content = addEventBody.content;
    } else if (addEventBody.type === TxEventType.HAILING_USER_UPDATES_ORDER) {
      txEvent.content = addEventBody.content;
    } else if (txEvent.type === TxEventType.HAILING_USER_CANCELS_ORDER) {
      txEvent.content = { charges: { cancellationFee: 0 } };
    } else if (addEventBody.type === TxEventType.HAILING_ADMIN_CANCELS_ORDER) {
      txEvent.content = addEventBody.content;
    }
    return txEvent;
  }
}
