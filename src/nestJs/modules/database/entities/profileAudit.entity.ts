import { ApiProperty } from "@nestjs/swagger";
import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Re<PERSON> } from "typeorm";

import { DefaultEntity } from "./defaultEntity";
import User from "./user.entity";
import { ProfileAuditActionType, ProfileAuditSourceType } from "../../audit/profileAudit/dto/profileAudit.dto";
import { ProfileAuditMetadata } from "../../audit/profileAudit/dto/profileAuditMetadata.dto";

@Entity()
export default class ProfileAudit extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  @ApiProperty()
  id: string;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn()
  @ApiProperty({ type: () => User })
  user: Relation<User>;

  @Column({
    type: "enum",
    enum: ProfileAuditActionType,
  })
  @ApiProperty({ type: "enum", enum: ProfileAuditActionType })
  action: ProfileAuditActionType;

  @Column("jsonb")
  @ApiProperty({ type: "object" })
  valueAfter: User;

  @Column()
  @ApiProperty()
  actionBy: string;

  @Column("jsonb")
  @ApiProperty({ type: "object" })
  metadata: ProfileAuditMetadata; //later

  @Column({
    type: "enum",
    enum: ProfileAuditSourceType,
  })
  @ApiProperty({ type: "enum", enum: ProfileAuditSourceType })
  source: ProfileAuditSourceType;
}
