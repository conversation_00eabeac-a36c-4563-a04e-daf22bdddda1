import { ApiProperty } from "@nestjs/swagger";
import {
  Column,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  Relation,
} from "typeorm";

import { DefaultEntity } from "./defaultEntity";
import Merchant from "./merchant.entity";
import Merchant<PERSON>ey from "./merchantKey.entity";
import WebhookEvent from "./webhookEvent.entity";

/**
 * Webhook entities for sending events to subscribing merchants
 */
@Entity()
@Index("merchant_deletedAt", ["merchant"], {
  where: '"deletedAt" IS NULL',
})
export default class Webhook extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  @ApiProperty()
  id: string;

  /**
   * The endpoint to send the webhook payload to
   */
  @Column()
  @ApiProperty()
  url: string;

  @ManyToOne(() => MerchantKey, { nullable: false })
  signatureKey: Relation<MerchantKey>;

  @ManyToOne(() => Merchant, { nullable: false })
  merchant: Relation<Merchant>;

  @OneToMany(() => WebhookEvent, (event) => event.webhook, { nullable: false, cascade: true })
  @ApiProperty()
  events?: Relation<WebhookEvent[]>;

  @DeleteDateColumn()
  deletedAt?: Date;
}
