import { ApiProperty } from "@nestjs/swagger";
import { Column, Entity, Index, ManyToOne, PrimaryGeneratedColumn, Relation } from "typeorm";

import { DefaultEntity } from "./defaultEntity";
import User from "./user.entity";

@Entity()
export default class UserNotificationToken extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Index({ unique: true })
  @Column()
  token: string;

  @Column()
  lastUpdateDate: Date;

  @ManyToOne(() => User, (user) => user.id)
  @ApiProperty({ type: () => User })
  user: Relation<User>;
}
