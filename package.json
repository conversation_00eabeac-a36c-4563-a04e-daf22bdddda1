{"name": "functions", "scripts": {"lint": "eslint --ext .js,.ts .", "build": "yarn preBuild && tsc", "preBuild": "./scripts/preBuild.sh", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "jest --testMatch=**/__tests__/**/*.spec.ts", "test:coverage": "jest --testMatch=**/__tests__/**/*.spec.ts --coverage", "test:integration": "jest --testMatch=**/__tests_integration__/**/*.spec.ts --runInBand", "test:firestoreRules": "firebase emulators:exec 'jest --testMatch=**/__tests_firestore_rules__/**/*.spec.ts' --log-verbosity SILENT", "typeorm": "node --require ts-node/register ./node_modules/typeorm/cli.js -d src/nestJs/modules/database/database.config.ts", "migrate:generate": "yarn typeorm migration:generate src/__migrations__/migration && echo 'Linting migration file...' && eslint --ext .js,.ts src/__migrations__/ --fix", "migrate:run": "yarn typeorm migration:run", "migrate:revert": "yarn typeorm migration:revert", "schema:log": "yarn typeorm schema:log", "schema:drop": "yarn typeorm schema:drop", "audit": "yarn audit --json > audit.jsonl || true && node scripts/audit.js", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org vis-mobility-limited --project backend-functions ./lib && sentry-cli sourcemaps upload --org vis-mobility-limited --project backend-functions ./lib"}, "engines": {"node": "20"}, "main": "lib/index.js", "dependencies": {"@babel/preset-env": "^7.20.2", "@google-cloud/bigquery": "^7.9.1", "@google-cloud/kms": "^4.5.0", "@google-cloud/pubsub": "^4.3.3", "@google-cloud/secret-manager": "^5.6.0", "@google-cloud/storage": "^7.15.1", "@google-cloud/tasks": "^6.1.0", "@googleapis/sqladmin": "^11.0.0", "@googlemaps/google-maps-services-js": "^3.3.16", "@googlemaps/places": "^1.6.0", "@googlemaps/routing": "^1.3.0", "@nestjs/axios": "^3.0.0", "@nestjs/cache-manager": "2.2.2", "@nestjs/common": "^9.4.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^9.4.0", "@nestjs/platform-express": "10.4.15", "@nestjs/schedule": "^4.0.0", "@nestjs/terminus": "^10.1.1", "@nestjs/testing": "^9.4.0", "@nestjs/typeorm": "^10.0.0", "@sendgrid/mail": "^8.1.3", "@sentry/cli": "^2.38.0", "@sentry/nestjs": "^8.35.0", "@sentry/profiling-node": "^8.35.0", "@side/jest-runtime": "^1.1.0", "@swc/core": "^1.12.14", "@swc/jest": "^0.2.39", "@tmcw/togeojson": "^7.1.2", "@turf/geojson-rbush": "^7.2.0", "@turf/turf": "^7.2.0", "@types/crypto-js": "^4.1.1", "@types/deep-diff": "^1.0.2", "@types/js-yaml": "^4.0.9", "@types/jsonpath-plus": "^5.0.2", "@types/jws": "^3.2.5", "@types/lodash": "^4.14.191", "agentkeepalive": "^4.2.1", "ajv": "^8.12.0", "archiver": "^7.0.1", "archiver-zip-encrypted": "^2.0.0", "async-retry": "^1.3.3", "axios": "^1.4.0", "axios-retry": "^3.4.0", "babel-jest": "^29.4.2", "bcrypt": "^5.1.1", "cache-manager": "5.7.6", "compare-versions": "^6.1.0", "cors": "^2.8.5", "cron-parser": "^4.9.0", "crypto-js": "^4.1.1", "csv-writer": "^1.6.0", "cybersource-rest-client": "^0.0.66", "dayjs": "^1.11.13", "decimal.js": "^10.4.3", "deep-diff": "^1.0.2", "express": "^4.21.2", "firebase": "^9.6.9", "firebase-admin": "^12.0.0", "firebase-functions": "4.9.0", "geojson": "^0.5.0", "googleapis-common": "^7.0.1", "i18next": "^22.4.12", "i18next-fs-backend": "^2.1.1", "joi": "^17.9.2", "js-yaml": "^4.1.0", "json-logic-js": "^2.0.5", "jsonpath-plus": "^10.3.0", "jwk-to-pem": "^2.0.7", "jws": "^4.0.0", "lodash": "^4.17.21", "memoizee": "^0.4.15", "moment": "^2.29.4", "moment-timezone": "^0.5.38", "nest-router": "^1.0.9", "nestjs-cls": "^3.6.0", "otplib": "^12.0.1", "pg": "^8.11.3", "ramda": "^0.31.3", "read-excel-file": "^5.6.1", "redis": "^5.6.0", "reflect-metadata": "^0.1.13", "retry-axios": "2.6.0", "rxjs": "^7.8.1", "ts-node": "^10.9.2", "turf": "^3.0.14", "twilio": "^4.23.0", "typeorm": "0.3.19", "xmldom": "^0.6.0"}, "devDependencies": {"@firebase/rules-unit-testing": "^3.0.2", "@nestjs/swagger": "^7.1.8", "@sentry/types": "^8.35.0", "@types/archiver": "^6.0.3", "@types/async-retry": "^1.4.9", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.17", "@types/geojson": "^7946.0.16", "@types/jest": "^29.4.0", "@types/joi": "^17.2.3", "@types/json-logic-js": "^2.0.7", "@types/memoizee": "^0.4.8", "@types/node": "^20.3.3", "@types/ramda": "^0.30.2", "@types/rbush": "^4.0.0", "@types/xmldom": "^0.1.34", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.1", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^8.8.0", "eslint-config-standard-with-typescript": "^22.0.0", "eslint-import-resolver-typescript": "^3.5.5", "eslint-plugin-import": "^2.30.0", "eslint-plugin-n": "^15.0.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-unused-imports": "^3.0.0", "firebase-functions-test": "^3.0.0", "jest": "^29.4.2", "module-alias": "^2.2.3", "prettier": "^2.8.8", "ts-jest": "^29.0.5", "ts-loader": "^9.4.4", "tsconfig-paths": "^4.2.0", "typescript": "*", "webpack": "^5.88.2", "webpack-cli": "^5.1.4", "webpack-node-externals": "^3.0.0"}, "private": true, "_moduleAliases": {"@tests": "lib/__tests__", "@migrations": "lib/__migrations__", "@functions": "lib/functions", "@static": "lib/static", "@legacy": "lib/legacy", "@nest": "lib/nestJs"}}