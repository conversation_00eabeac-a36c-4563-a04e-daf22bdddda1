{"testEnvironment": "node", "transform": {"^.+\\.ts?$": "@swc/jest", "^.+\\.[t|j]sx?$": "babel-jest"}, "modulePathIgnorePatterns": [], "testMatch": [], "moduleNameMapper": {"^@tests/(.*)$": "<rootDir>/src/__tests__/$1", "^@migrations/(.*)$": "<rootDir>/src/__migrations__/$1", "^@functions/(.*)$": "<rootDir>/src/functions/$1", "^@static/(.*)$": "<rootDir>/src/static/$1", "^@legacy/(.*)$": "<rootDir>/src/legacy/$1", "^@nest/(.*)$": "<rootDir>/src/nestJs/$1"}, "runtime": "@side/jest-runtime"}